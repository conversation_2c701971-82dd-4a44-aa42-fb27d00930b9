-- إصلاح مشاكل Row Level Security وإنشاء الجداول المطلوبة
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- 1. إنشاء جدول profiles إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name VARCHA<PERSON>(255),
    avatar_url TEXT,
    organization_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. إنشاء جدول organizations إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255),
    business_type VARCHAR(100),
    logo_url TEXT,
    commercial_register VARCHAR(100),
    tax_number VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(2) DEFAULT 'SA',
    registration_date DATE,
    entity_type VARCHAR(100),
    admin_phone VARCHAR(20),
    admin_email VARCHAR(255),
    digital_stamp_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. إضافة foreign key constraint لـ organization_id في profiles
ALTER TABLE public.profiles 
ADD CONSTRAINT fk_profiles_organization 
FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE SET NULL;

-- 4. إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_profiles_organization_id ON public.profiles(organization_id);
CREATE INDEX IF NOT EXISTS idx_organizations_owner_id ON public.organizations(owner_id);
CREATE INDEX IF NOT EXISTS idx_organizations_email ON public.organizations(email);

-- 5. إنشاء قيد فريد على owner_id (مؤسسة واحدة لكل مستخدم)
ALTER TABLE public.organizations 
DROP CONSTRAINT IF EXISTS unique_owner_id;
ALTER TABLE public.organizations 
ADD CONSTRAINT unique_owner_id UNIQUE (owner_id);

-- 6. تمكين Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- 7. حذف السياسات القديمة إذا كانت موجودة
DROP POLICY IF EXISTS "Users can read their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can delete their own profile" ON public.profiles;

DROP POLICY IF EXISTS "Users can read their own organization" ON public.organizations;
DROP POLICY IF EXISTS "Users can insert their own organization" ON public.organizations;
DROP POLICY IF EXISTS "Users can update their own organization" ON public.organizations;
DROP POLICY IF EXISTS "Users can delete their own organization" ON public.organizations;

-- 8. إنشاء سياسات جديدة للـ profiles
CREATE POLICY "Enable read access for users to their own profile"
ON public.profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Enable insert access for users to their own profile"
ON public.profiles FOR INSERT
WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update access for users to their own profile"
ON public.profiles FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable delete access for users to their own profile"
ON public.profiles FOR DELETE
USING (auth.uid() = id);

-- 9. إنشاء سياسات جديدة للـ organizations
CREATE POLICY "Enable read access for users to their own organization"
ON public.organizations FOR SELECT
USING (auth.uid() = owner_id);

CREATE POLICY "Enable insert access for users to create their organization"
ON public.organizations FOR INSERT
WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Enable update access for users to their own organization"
ON public.organizations FOR UPDATE
USING (auth.uid() = owner_id)
WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Enable delete access for users to their own organization"
ON public.organizations FOR DELETE
USING (auth.uid() = owner_id);

-- 10. إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 11. إنشاء triggers لتحديث updated_at
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_organizations_updated_at ON public.organizations;
CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON public.organizations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 12. إنشاء دالة لإنشاء ملف شخصي تلقائياً عند تسجيل مستخدم جديد
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. إنشاء trigger لإنشاء ملف شخصي تلقائياً
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 14. منح الصلاحيات المناسبة
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.organizations TO authenticated;
GRANT SELECT ON public.profiles TO anon;
GRANT SELECT ON public.organizations TO anon;

-- 15. إنشاء bucket للشعارات في Storage (إذا لم يكن موجوداً)
INSERT INTO storage.buckets (id, name, public)
VALUES ('organization-logos', 'organization-logos', true)
ON CONFLICT (id) DO NOTHING;

-- 16. إنشاء سياسة للـ Storage
CREATE POLICY "Users can upload their organization logos"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'organization-logos' 
    AND auth.role() = 'authenticated'
);

CREATE POLICY "Users can view organization logos"
ON storage.objects FOR SELECT
USING (bucket_id = 'organization-logos');

CREATE POLICY "Users can update their organization logos"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'organization-logos' 
    AND auth.role() = 'authenticated'
);

CREATE POLICY "Users can delete their organization logos"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'organization-logos' 
    AND auth.role() = 'authenticated'
);

-- 17. تفعيل RLS على storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
