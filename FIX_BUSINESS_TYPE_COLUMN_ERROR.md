# 🔧 إصلاح خطأ عمود business_type في جدول organizations

## 🚨 **المشكلة المحددة:**

### **الخطأ:**
```
Error: Organization creation error details: {}
Error: Full error object: {
  "code": "PGRST204",
  "details": null,
  "hint": null,
  "message": "Could not find the 'business_type' column of 'organizations' in the schema cache"
}
```

### **السبب:**
- الكود يحاول استخدام عمود `business_type` في جدول `organizations`
- لكن هذا العمود غير موجود في قاعدة البيانات الفعلية
- هناك تضارب بين ملفات SQL المختلفة في المشروع

---

## 🔍 **التشخيص:**

### **الملفات المتضاربة:**

#### **1. `supabase_organizations_table.sql` (يحتوي على business_type):**
```sql
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255),
    business_type VARCHAR(100),  -- ✅ موجود هنا
    logo_url TEXT,
    commercial_register VARCHAR(100),
    -- ... باقي الأعمدة
);
```

#### **2. `supabase/migrations/001_initial_schema.sql` (لا يحتوي على business_type):**
```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100),
    tax_number VARCHAR(15),
    commercial_register VARCHAR(10),
    -- ❌ لا يوجد business_type هنا
    -- ... باقي الأعمدة
);
```

### **النتيجة:**
- قاعدة البيانات تم إنشاؤها بناءً على `001_initial_schema.sql`
- الكود يحاول استخدام الأعمدة من `supabase_organizations_table.sql`
- هذا يسبب خطأ "column not found"

---

## ✅ **الحل السريع:**

### **الخطوة 1: تشغيل ملف الإصلاح**

#### **افتح Supabase Dashboard:**
1. اذهب إلى [Supabase Dashboard](https://app.supabase.com/)
2. اختر مشروعك
3. اذهب إلى **SQL Editor**

#### **انسخ والصق الكود التالي:**
```sql
-- إصلاح جدول organizations بإضافة الأعمدة المفقودة
DO $$
BEGIN
    -- إضافة عمود business_type
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'business_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN business_type VARCHAR(100);
        RAISE NOTICE 'تم إضافة عمود business_type';
    END IF;
    
    -- إضافة عمود email
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'email'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN email VARCHAR(255);
        RAISE NOTICE 'تم إضافة عمود email';
    END IF;
    
    -- إضافة عمود address
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'address'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN address TEXT;
        RAISE NOTICE 'تم إضافة عمود address';
    END IF;
    
    -- إضافة عمود registration_date
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'registration_date'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN registration_date DATE;
        RAISE NOTICE 'تم إضافة عمود registration_date';
    END IF;
    
    -- إضافة عمود entity_type
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'entity_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN entity_type VARCHAR(100);
        RAISE NOTICE 'تم إضافة عمود entity_type';
    END IF;
    
    -- إضافة عمود admin_phone
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'admin_phone'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN admin_phone VARCHAR(20);
        RAISE NOTICE 'تم إضافة عمود admin_phone';
    END IF;
    
    -- إضافة عمود admin_email
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'admin_email'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN admin_email VARCHAR(255);
        RAISE NOTICE 'تم إضافة عمود admin_email';
    END IF;
    
    -- إضافة عمود digital_stamp_url
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'digital_stamp_url'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.organizations 
        ADD COLUMN digital_stamp_url TEXT;
        RAISE NOTICE 'تم إضافة عمود digital_stamp_url';
    END IF;
    
END $$;
```

#### **اضغط "Run" لتنفيذ الكود**

### **الخطوة 2: التحقق من النتيجة**

#### **تشغيل هذا الاستعلام للتأكد:**
```sql
-- عرض جميع أعمدة جدول organizations
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'organizations' 
AND table_schema = 'public'
ORDER BY ordinal_position;
```

#### **النتيجة المتوقعة:**
يجب أن ترى جميع الأعمدة التالية:
- ✅ `business_type` - VARCHAR(100)
- ✅ `email` - VARCHAR(255)
- ✅ `address` - TEXT
- ✅ `registration_date` - DATE
- ✅ `entity_type` - VARCHAR(100)
- ✅ `admin_phone` - VARCHAR(20)
- ✅ `admin_email` - VARCHAR(255)
- ✅ `digital_stamp_url` - TEXT

---

## 🧪 **اختبار الحل:**

### **الخطوة 1: تحديث الصفحة**
- اذهب إلى `http://localhost:3000/auth/signin`
- سجل الدخول أو حدث الصفحة

### **الخطوة 2: تجربة إنشاء مؤسسة**
- إذا تم توجيهك إلى `/setup-organization`
- املأ النموذج واضغط "إنشاء المؤسسة"

### **الخطوة 3: فحص Console**
- افتح Console المتصفح (F12)
- يجب ألا ترى خطأ `business_type` بعد الآن

### **النتيجة المتوقعة:**
```
✅ تم إنشاء المؤسسة بنجاح
✅ تم توجيهك إلى /dashboard
✅ لا توجد أخطاء في Console
```

---

## 🔧 **حل بديل (إذا لم يعمل الحل الأول):**

### **إعادة إنشاء الجدول بالكامل:**

#### **⚠️ تحذير: هذا سيحذف جميع البيانات الموجودة**

```sql
-- حذف الجدول القديم (احذر: سيحذف جميع البيانات)
DROP TABLE IF EXISTS public.organizations CASCADE;

-- إنشاء الجدول الجديد مع جميع الأعمدة
CREATE TABLE public.organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255),
    business_type VARCHAR(100),
    logo_url TEXT,
    commercial_register VARCHAR(100),
    tax_number VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(2) DEFAULT 'SA',
    registration_date DATE,
    entity_type VARCHAR(100),
    admin_phone VARCHAR(20),
    admin_email VARCHAR(255),
    digital_stamp_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهرس للأداء
CREATE INDEX idx_organizations_owner_id ON public.organizations(owner_id);

-- تفعيل RLS
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- إضافة سياسات الأمان
CREATE POLICY "Users can view their own organizations" ON public.organizations
    FOR SELECT USING (owner_id = auth.uid());

CREATE POLICY "Users can insert their own organizations" ON public.organizations
    FOR INSERT WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own organizations" ON public.organizations
    FOR UPDATE USING (owner_id = auth.uid());

CREATE POLICY "Users can delete their own organizations" ON public.organizations
    FOR DELETE USING (owner_id = auth.uid());
```

---

## 📊 **التحقق النهائي:**

### **1. فحص بنية الجدول:**
```sql
\d public.organizations
```

### **2. فحص السياسات:**
```sql
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'organizations';
```

### **3. اختبار إدراج بيانات:**
```sql
-- اختبار إدراج (استبدل USER_ID بـ ID المستخدم الفعلي)
INSERT INTO public.organizations (name, owner_id, business_type, email)
VALUES ('شركة تجريبية', 'USER_ID_HERE', 'technology', '<EMAIL>');
```

---

## 🎯 **النتيجة المتوقعة:**

### **✅ بعد تطبيق الحل:**

#### **في التطبيق:**
- ✅ تسجيل دخول ناجح
- ✅ إنشاء مؤسسة ناجح
- ✅ توجيه إلى `/dashboard`
- ✅ لا توجد أخطاء

#### **في Console المتصفح:**
```
Starting organization check for user: abc123
Organizations table is accessible
Profile data retrieved: { organization_id: null }
organization_id is empty, searching in organizations table for owner_id: abc123
No organization found for user, redirecting to setup-organization
```

#### **عند إنشاء مؤسسة جديدة:**
```
✅ Organization created successfully
✅ Profile updated with organization_id
✅ Redirecting to dashboard
```

---

## 🚀 **الخلاصة:**

**المشكلة:** عمود `business_type` مفقود من جدول `organizations`

**الحل:** إضافة الأعمدة المفقودة باستخدام `ALTER TABLE`

**النتيجة:** تطبيق يعمل بشكل كامل بدون أخطاء

**الخطوة التالية:** تشغيل ملف SQL في Supabase Dashboard

---

*دليل الإصلاح - إعداد: ${new Date().toLocaleString('ar-SA')}*
*بواسطة: Augment Agent*
*حالة الدليل: ✅ جاهز للتطبيق الفوري*
