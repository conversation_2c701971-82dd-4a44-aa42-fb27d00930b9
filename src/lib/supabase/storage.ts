import { supabase } from './client'

/**
 * رفع ملف إلى Supabase Storage
 */
export async function uploadFile(
  bucket: string,
  path: string,
  file: File,
  options?: {
    cacheControl?: string
    contentType?: string
    upsert?: boolean
  }
) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: options?.cacheControl || '3600',
        contentType: options?.contentType || file.type,
        upsert: options?.upsert || false
      })

    if (error) {
      console.error('Upload error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Upload error:', error)
    return { data: null, error }
  }
}

/**
 * الحصول على URL عام لملف
 */
export function getPublicUrl(bucket: string, path: string) {
  try {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path)

    return data.publicUrl
  } catch (error) {
    console.error('Get public URL error:', error)
    return null
  }
}

/**
 * حذف ملف من Storage
 */
export async function deleteFile(bucket: string, paths: string[]) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .remove(paths)

    if (error) {
      console.error('Delete error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Delete error:', error)
    return { data: null, error }
  }
}

/**
 * إنشاء bucket جديد (للاستخدام في الإعداد)
 */
export async function createBucket(
  bucketName: string,
  options?: {
    public?: boolean
    allowedMimeTypes?: string[]
    fileSizeLimit?: number
  }
) {
  try {
    const { data, error } = await supabase.storage.createBucket(bucketName, {
      public: options?.public || true,
      allowedMimeTypes: options?.allowedMimeTypes || ['image/*'],
      fileSizeLimit: options?.fileSizeLimit || 2097152 // 2MB
    })

    if (error) {
      console.error('Create bucket error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Create bucket error:', error)
    return { data: null, error }
  }
}

/**
 * التحقق من وجود bucket
 */
export async function checkBucketExists(bucketName: string) {
  try {
    const { data, error } = await supabase.storage.getBucket(bucketName)
    
    if (error) {
      return false
    }

    return !!data
  } catch (error) {
    return false
  }
}

/**
 * إعداد buckets المطلوبة للمشروع
 */
export async function setupStorageBuckets() {
  const buckets = [
    {
      name: 'organization-logos',
      options: {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        fileSizeLimit: 2097152 // 2MB
      }
    },
    {
      name: 'user-avatars',
      options: {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        fileSizeLimit: 1048576 // 1MB
      }
    },
    {
      name: 'documents',
      options: {
        public: false,
        allowedMimeTypes: ['application/pdf', 'image/*'],
        fileSizeLimit: 10485760 // 10MB
      }
    }
  ]

  const results = []

  for (const bucket of buckets) {
    const exists = await checkBucketExists(bucket.name)
    
    if (!exists) {
      console.log(`Creating bucket: ${bucket.name}`)
      const result = await createBucket(bucket.name, bucket.options)
      results.push({ bucket: bucket.name, ...result })
    } else {
      console.log(`Bucket already exists: ${bucket.name}`)
      results.push({ bucket: bucket.name, data: 'exists', error: null })
    }
  }

  return results
}

// تصدير الثوابت المفيدة
export const STORAGE_BUCKETS = {
  ORGANIZATION_LOGOS: 'organization-logos',
  USER_AVATARS: 'user-avatars',
  DOCUMENTS: 'documents'
} as const

export const FILE_SIZE_LIMITS = {
  LOGO: 2 * 1024 * 1024, // 2MB
  AVATAR: 1 * 1024 * 1024, // 1MB
  DOCUMENT: 10 * 1024 * 1024 // 10MB
} as const

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp'
] as const
