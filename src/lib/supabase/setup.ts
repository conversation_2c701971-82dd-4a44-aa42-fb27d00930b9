import { supabase } from './client'

/**
 * إعداد Supabase Storage Buckets المطلوبة للمشروع
 */
export async function setupStorageBuckets() {
  const buckets = [
    {
      name: 'organization-logos',
      options: {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        fileSizeLimit: 2097152 // 2MB
      }
    },
    {
      name: 'user-avatars',
      options: {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        fileSizeLimit: 1048576 // 1MB
      }
    },
    {
      name: 'documents',
      options: {
        public: false,
        allowedMimeTypes: ['application/pdf', 'image/*', 'text/*'],
        fileSizeLimit: 10485760 // 10MB
      }
    }
  ]

  const results = []

  for (const bucket of buckets) {
    try {
      // التحقق من وجود الـ bucket
      const { data: existingBucket, error: getBucketError } = await supabase.storage.getBucket(bucket.name)
      
      if (getBucketError && getBucketError.message.includes('not found')) {
        // إنشاء الـ bucket إذا لم يكن موجوداً
        console.log(`Creating bucket: ${bucket.name}`)
        const { data, error } = await supabase.storage.createBucket(bucket.name, bucket.options)
        
        if (error) {
          console.error(`Error creating bucket ${bucket.name}:`, error)
          results.push({ bucket: bucket.name, success: false, error: error.message })
        } else {
          console.log(`Successfully created bucket: ${bucket.name}`)
          results.push({ bucket: bucket.name, success: true, data })
        }
      } else if (existingBucket) {
        console.log(`Bucket already exists: ${bucket.name}`)
        results.push({ bucket: bucket.name, success: true, data: 'already_exists' })
      } else {
        console.error(`Error checking bucket ${bucket.name}:`, getBucketError)
        results.push({ bucket: bucket.name, success: false, error: getBucketError?.message })
      }
    } catch (error) {
      console.error(`Unexpected error with bucket ${bucket.name}:`, error)
      results.push({ bucket: bucket.name, success: false, error: 'unexpected_error' })
    }
  }

  return results
}

/**
 * رفع ملف إلى bucket محدد مع إنشاء الـ bucket إذا لم يكن موجوداً
 */
export async function uploadFileWithBucketCreation(
  bucketName: string,
  fileName: string,
  file: File,
  options?: {
    public?: boolean
    allowedMimeTypes?: string[]
    fileSizeLimit?: number
  }
) {
  try {
    // التحقق من وجود الـ bucket
    const { data: bucketData, error: bucketError } = await supabase.storage.getBucket(bucketName)
    
    if (bucketError && bucketError.message.includes('not found')) {
      // إنشاء الـ bucket
      const { error: createError } = await supabase.storage.createBucket(bucketName, {
        public: options?.public ?? true,
        allowedMimeTypes: options?.allowedMimeTypes ?? ['image/*'],
        fileSizeLimit: options?.fileSizeLimit ?? 2097152
      })
      
      if (createError) {
        console.error('Bucket creation error:', createError)
        return { data: null, error: createError }
      }
    }
    
    // رفع الملف
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      return { data: null, error }
    }

    // الحصول على URL العام
    const { data: { publicUrl } } = supabase.storage
      .from(bucketName)
      .getPublicUrl(fileName)

    return { data: { ...data, publicUrl }, error: null }
  } catch (error) {
    console.error('Upload error:', error)
    return { data: null, error }
  }
}

/**
 * تحويل ملف إلى base64 كبديل عند فشل الرفع
 */
export function fileToBase64(file: File): Promise<string | null> {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result as string)
    }
    reader.onerror = () => {
      resolve(null)
    }
    reader.readAsDataURL(file)
  })
}

/**
 * التحقق من صحة نوع الملف
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.some(type => {
    if (type.endsWith('/*')) {
      return file.type.startsWith(type.slice(0, -1))
    }
    return file.type === type
  })
}

/**
 * التحقق من حجم الملف
 */
export function validateFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize
}

/**
 * تنظيف اسم الملف
 */
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .toLowerCase()
}

/**
 * إنشاء اسم ملف فريد
 */
export function generateUniqueFileName(originalName: string, userId: string): string {
  const extension = originalName.split('.').pop() || ''
  const baseName = originalName.replace(`.${extension}`, '')
  const sanitizedBaseName = sanitizeFileName(baseName)
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  
  return `${userId}_${sanitizedBaseName}_${timestamp}_${random}.${extension}`
}

// تصدير الثوابت
export const STORAGE_BUCKETS = {
  ORGANIZATION_LOGOS: 'organization-logos',
  USER_AVATARS: 'user-avatars',
  DOCUMENTS: 'documents'
} as const

export const FILE_SIZE_LIMITS = {
  LOGO: 2 * 1024 * 1024, // 2MB
  AVATAR: 1 * 1024 * 1024, // 1MB
  DOCUMENT: 10 * 1024 * 1024 // 10MB
} as const

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp'
] as const
