'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

/**
 * Hook للتحقق من وجود route معين قبل التوجيه إليه
 */
export function useRouteChecker() {
  const router = useRouter()
  const [isChecking, setIsChecking] = useState(false)

  /**
   * التحقق من وجود route والتوجيه إليه أو إلى fallback
   * @param primaryRoute - الـ route الأساسي المراد التوجيه إليه
   * @param fallbackRoute - الـ route البديل في حالة عدم وجود الأساسي
   * @returns Promise<boolean> - true إذا تم التوجيه للـ route الأساسي، false للبديل
   */
  const checkAndNavigate = async (primaryRoute: string, fallbackRoute: string): Promise<boolean> => {
    setIsChecking(true)
    
    try {
      // محاولة التوجيه إلى الـ route الأساسي
      router.push(primaryRoute)
      setIsChecking(false)
      return true
    } catch (error) {
      console.warn(`Route ${primaryRoute} not accessible, falling back to ${fallbackRoute}`)
      
      try {
        // التوجيه إلى الـ route البديل
        router.push(fallbackRoute)
        setIsChecking(false)
        return false
      } catch (fallbackError) {
        console.error(`Both routes failed: ${primaryRoute} and ${fallbackRoute}`, fallbackError)
        // التوجيه إلى الصفحة الرئيسية كحل أخير
        router.push('/')
        setIsChecking(false)
        return false
      }
    }
  }

  /**
   * التوجيه الآمن مع التحقق من وجود الصفحة
   * @param route - الـ route المراد التوجيه إليه
   * @param fallback - الـ route البديل (افتراضي: '/')
   */
  const safeNavigate = async (route: string, fallback: string = '/') => {
    return checkAndNavigate(route, fallback)
  }

  /**
   * التوجيه الذكي بناءً على حالة المستخدم
   * @param hasOrganization - هل المستخدم لديه مؤسسة
   * @param isAuthenticated - هل المستخدم مصادق
   * @param currentPath - المسار الحالي (اختياري)
   */
  const smartNavigate = async (hasOrganization: boolean, isAuthenticated: boolean, currentPath?: string) => {
    // إذا لم يكن مصادق وهو في صفحة تسجيل الدخول، لا تفعل شيء
    if (!isAuthenticated) {
      if (currentPath === '/auth/signin' || currentPath?.startsWith('/auth/')) {
        return // ابق في صفحة المصادقة
      }
      router.push('/auth/signin')
      return
    }

    // إذا كان مصادق لكن بدون مؤسسة
    if (!hasOrganization) {
      if (currentPath === '/setup-organization') {
        return // ابق في صفحة إعداد المؤسسة
      }
      router.push('/setup-organization')
      return
    }

    // المستخدم مصادق ولديه مؤسسة
    if (currentPath === '/dashboard' || currentPath === '/organization/settings') {
      return // ابق في الصفحة الحالية إذا كانت مناسبة
    }

    // جرب dashboard أولاً
    const dashboardSuccess = await checkAndNavigate('/dashboard', '/organization/settings')

    if (!dashboardSuccess) {
      console.log('Dashboard not accessible, redirected to organization settings')
    }
  }

  return {
    isChecking,
    checkAndNavigate,
    safeNavigate,
    smartNavigate
  }
}

/**
 * Hook مبسط للتحقق من حالة المستخدم والتوجيه المناسب
 */
export function useUserStatusChecker() {
  const { smartNavigate, isChecking } = useRouteChecker()
  const [hasChecked, setHasChecked] = useState(false)
  const [lastUserId, setLastUserId] = useState<string | null>(null)

  /**
   * فحص حالة المستخدم والتوجيه المناسب
   * @param user - بيانات المستخدم من AuthContext
   * @param loading - حالة التحميل من AuthContext
   */
  const checkUserStatus = async (user: any, loading: boolean) => {
    // إذا كان لا يزال يحمل، لا تفعل شيء
    if (loading) {
      return
    }

    const currentUserId = user?.id || null

    // إذا تغير المستخدم، أعد تعيين حالة الفحص
    if (currentUserId !== lastUserId) {
      setLastUserId(currentUserId)
      setHasChecked(false)
    }

    // إذا تم الفحص بالفعل لنفس المستخدم، لا تكرر
    if (hasChecked && currentUserId === lastUserId) {
      return
    }

    // إذا لم يكن هناك مستخدم، لا تفعل شيء (ابق في صفحة تسجيل الدخول)
    if (!user) {
      setHasChecked(true)
      return
    }

    const isAuthenticated = !!user
    const hasOrganization = !!(user?.organization)

    // الحصول على المسار الحالي
    const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''

    await smartNavigate(hasOrganization, isAuthenticated, currentPath)
    setHasChecked(true)
  }

  /**
   * إعادة تعيين حالة الفحص (مفيد عند تغيير المستخدم)
   */
  const resetCheck = () => {
    setHasChecked(false)
    setLastUserId(null)
  }

  return {
    checkUserStatus,
    resetCheck,
    isChecking,
    hasChecked
  }
}

/**
 * Routes شائعة مع fallbacks
 */
export const COMMON_ROUTES = {
  DASHBOARD: {
    primary: '/dashboard',
    fallback: '/organization/settings'
  },
  ORGANIZATION_SETTINGS: {
    primary: '/organization/settings',
    fallback: '/'
  },
  SETUP_ORGANIZATION: {
    primary: '/setup-organization',
    fallback: '/auth/signin'
  },
  AUTH_SIGNIN: {
    primary: '/auth/signin',
    fallback: '/'
  }
} as const
