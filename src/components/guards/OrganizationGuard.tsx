'use client'

import { AuthGuard } from './AuthGuard'

interface OrganizationGuardProps {
  children: React.ReactNode
}

/**
 * مكون حماية للصفحات التي تتطلب مؤسسة
 * يتحقق من:
 * 1. وجود جلسة صالحة
 * 2. تأكيد البريد الإلكتروني
 * 3. وجود مؤسسة مرتبطة بالمستخدم
 */
export function OrganizationGuard({ children }: OrganizationGuardProps) {
  return (
    <AuthGuard requireOrganization={true}>
      {children}
    </AuthGuard>
  )
}
