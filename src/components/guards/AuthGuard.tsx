'use client'

import { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { Loader2, AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import type { User } from '@supabase/supabase-js'

interface AuthGuardProps {
  children: React.ReactNode
  requireOrganization?: boolean
  redirectTo?: string
}

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  hasOrganization: boolean
  emailConfirmed: boolean
  error: string | null
}

export function AuthGuard({ 
  children, 
  requireOrganization = false,
  redirectTo = '/auth/signin'
}: AuthGuardProps) {
  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    hasOrganization: false,
    emailConfirmed: false,
    error: null
  })
  
  const hasCheckedAuth = useRef(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const checkAuthAndOrganization = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))

      console.log('AuthGuard: Starting authentication check...')

      // إعداد timeout لمدة 10 ثوان
      timeoutRef.current = setTimeout(() => {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'انتهت مهلة التحقق من المصادقة'
        }))
      }, 10000)

      // التحقق من الجلسة والمستخدم
      const [sessionResult, userResult] = await Promise.all([
        supabase.auth.getSession(),
        supabase.auth.getUser()
      ])

      // إلغاء timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }

      const { data: { session }, error: sessionError } = sessionResult
      const { data: { user }, error: userError } = userResult

      if (sessionError || userError) {
        console.error('AuthGuard: Auth error:', { sessionError, userError })
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'حدث خطأ في التحقق من المصادقة'
        }))
        return
      }

      console.log('AuthGuard: Session check result:', {
        hasSession: !!session,
        hasUser: !!user,
        emailConfirmed: user?.email_confirmed_at ? true : false
      })

      // إذا لم تكن هناك جلسة أو مستخدم
      if (!session || !user) {
        console.log('AuthGuard: No session or user, redirecting to signin')
        router.push(redirectTo)
        return
      }

      // التحقق من تأكيد البريد الإلكتروني
      const emailConfirmed = !!user.email_confirmed_at
      if (!emailConfirmed) {
        console.log('AuthGuard: Email not confirmed, redirecting to signin')
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'يجب تأكيد البريد الإلكتروني أولاً'
        }))
        setTimeout(() => router.push('/auth/signin'), 2000)
        return
      }

      // إذا كانت الحماية تتطلب مؤسسة، تحقق من وجودها
      let hasOrganization = false
      if (requireOrganization) {
        console.log('AuthGuard: Checking organization for user:', user.id)
        
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('organization_id')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('AuthGuard: Profile fetch error:', profileError)
          if (profileError.code === 'PGRST116') {
            // لا يوجد ملف شخصي، إنشاء واحد
            console.log('AuthGuard: Creating profile for user')
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: user.id,
                full_name: user.user_metadata?.full_name || user.email,
                avatar_url: user.user_metadata?.avatar_url
              })
            
            if (createError) {
              console.error('AuthGuard: Profile creation error:', createError)
            }
          }
        } else {
          hasOrganization = !!profile?.organization_id
          console.log('AuthGuard: Organization check result:', {
            hasProfile: !!profile,
            organizationId: profile?.organization_id,
            hasOrganization
          })
        }

        // إذا لم تكن هناك مؤسسة، وجه إلى إعداد المؤسسة
        if (!hasOrganization) {
          console.log('AuthGuard: No organization, redirecting to setup')
          router.push('/setup-organization')
          return
        }
      }

      // تحديث الحالة بنجاح
      setAuthState({
        user,
        isLoading: false,
        isAuthenticated: true,
        hasOrganization,
        emailConfirmed,
        error: null
      })

      console.log('AuthGuard: Authentication successful')

    } catch (error) {
      console.error('AuthGuard: Unexpected error:', error)
      
      // إلغاء timeout في حالة الخطأ
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
      
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'حدث خطأ غير متوقع في التحقق من المصادقة'
      }))
    }
  }

  const retryAuth = () => {
    hasCheckedAuth.current = false
    setAuthState(prev => ({ ...prev, error: null }))
    checkAuthAndOrganization()
  }

  useEffect(() => {
    // منع تشغيل useEffect مرتين
    if (hasCheckedAuth.current) return
    hasCheckedAuth.current = true

    checkAuthAndOrganization()

    // تنظيف timeout عند إلغاء المكون
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [requireOrganization, redirectTo, router])

  // شاشة التحميل
  if (authState.isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            جاري التحقق من المصادقة...
          </h2>
          <p className="text-gray-600">
            {requireOrganization 
              ? 'يتم التحقق من حالة المصادقة والمؤسسة'
              : 'يتم التحقق من حالة المصادقة'
            }
          </p>
        </div>
      </div>
    )
  }

  // شاشة الخطأ
  if (authState.error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            خطأ في التحقق من المصادقة
          </h2>
          <p className="text-gray-600 mb-6">
            {authState.error}
          </p>
          <div className="space-y-3">
            <Button
              onClick={retryAuth}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              <RefreshCw className="h-5 w-5 ml-2" />
              إعادة المحاولة
            </Button>
            <Button
              onClick={() => router.push(redirectTo)}
              variant="outline"
              className="w-full"
            >
              الذهاب لتسجيل الدخول
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // إذا وصل هنا، فالمصادقة ناجحة
  return <>{children}</>
}
