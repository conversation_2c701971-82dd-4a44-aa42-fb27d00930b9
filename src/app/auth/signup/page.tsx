'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { signUpSchema, type SignUpFormData } from '@/lib/validations/auth'
import { useAuth } from '@/contexts/AuthContext'
import { useAuthPageProtection } from '@/hooks/useAuthGuard'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Alert } from '@/components/ui/Alert'
import { Loader2, Eye, EyeOff, CheckCircle } from 'lucide-react'

export default function SignUpPage() {
  // يجب أن تكون جميع الـ hooks في البداية وبنفس الترتيب دائماً
  const router = useRouter()
  const { signUp, loading } = useAuth()
  const { isLoading: authGuardLoading } = useAuthPageProtection()

  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState<string>('')

  // useForm يجب أن يكون مع باقي الـ hooks
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema)
  })

  // إظهار شاشة تحميل أثناء فحص حالة المصادقة
  if (authGuardLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: '#f9fafb' }}>
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 text-blue-600 animate-spin mb-4" />
          <p className="text-gray-600">جاري التحقق من حالة المصادقة...</p>
        </div>
      </div>
    )
  }

  const onSubmit = async (data: SignUpFormData) => {
    try {
      setError(null)
      const result = await signUp(data.email, data.password, {
        full_name: data.full_name,
        phone: data.phone,
        country: data.country
      })

      if (result.error) {
        setError(result.error.message || 'حدث خطأ أثناء إنشاء الحساب')
      } else if (result.needsEmailConfirmation) {
        setSuccess(true)
        setSuccessMessage(result.message || 'تم إرسال رابط تأكيد إلى بريدك الإلكتروني')
      } else {
        // User is confirmed immediately, go to organization setup
        router.push('/setup-organization')
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-4 text-2xl font-bold text-gray-900">
            تم إنشاء الحساب بنجاح!
          </h2>
          <p className="mt-2 text-gray-600">
            {successMessage}
          </p>
          <div className="mt-6">
            <Link href="/auth/signin">
              <Button>
                العودة إلى تسجيل الدخول
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#f9fafb' }}>
      <div className="flex items-center justify-center">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-blue-100">
              <span className="text-3xl font-bold text-blue-600">O</span>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              إنشاء حساب جديد في OZOO
            </h2>
            <p className="mt-3 text-center text-sm text-gray-600">
              انضم إلى آلاف الشركات التي تثق في OZOO
            </p>
            <p className="mt-2 text-center text-sm text-gray-600">
              لديك حساب بالفعل؟{' '}
              <Link
                href="/auth/signin"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                تسجيل الدخول
              </Link>
            </p>
          </div>

          <div className="mt-8 bg-white rounded-lg shadow-md p-6 sm:p-8">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              {error && (
                <Alert variant="destructive">
                  {error}
                </Alert>
              )}

              <div className="space-y-5">
                <div>
                  <Label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الكامل
                  </Label>
                  <Input
                    id="full_name"
                    type="text"
                    autoComplete="name"
                    placeholder="أدخل اسمك الكامل"
                    {...register('full_name')}
                    className={`w-full px-4 py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${errors.full_name ? 'border-red-500' : ''}`}
                  />
                  {errors.full_name && (
                    <p className="mt-2 text-sm text-red-600">{errors.full_name.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    autoComplete="email"
                    placeholder="<EMAIL>"
                    {...register('email')}
                    className={`w-full px-4 py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${errors.email ? 'border-red-500' : ''}`}
                  />
                  {errors.email && (
                    <p className="mt-2 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الجوال
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+966501234567"
                    {...register('phone')}
                    className={`w-full px-4 py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${errors.phone ? 'border-red-500' : ''}`}
                  />
                  {errors.phone && (
                    <p className="mt-2 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
                    الدولة
                  </Label>
                  <select
                    id="country"
                    {...register('country')}
                    className={`w-full px-4 py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors disabled:cursor-not-allowed disabled:opacity-50 ${
                      errors.country ? 'border-red-500' : ''
                    }`}
                  >
                    <option value="">اختر الدولة</option>
                    <option value="SA">المملكة العربية السعودية</option>
                    <option value="AE">الإمارات العربية المتحدة</option>
                    <option value="KW">الكويت</option>
                    <option value="QA">قطر</option>
                    <option value="BH">البحرين</option>
                    <option value="OM">عمان</option>
                    <option value="JO">الأردن</option>
                    <option value="LB">لبنان</option>
                    <option value="EG">مصر</option>
                  </select>
                  {errors.country && (
                    <p className="mt-2 text-sm text-red-600">{errors.country.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور
                  </Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="new-password"
                      placeholder="أدخل كلمة مرور قوية"
                      {...register('password')}
                      className={`w-full px-4 py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${errors.password ? 'border-red-500' : ''}`}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 left-0 pl-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="mt-2 text-sm text-red-600">{errors.password.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                    تأكيد كلمة المرور
                  </Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      autoComplete="new-password"
                      placeholder="أعد إدخال كلمة المرور"
                      {...register('confirmPassword')}
                      className={`w-full px-4 py-3 bg-white border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${errors.confirmPassword ? 'border-red-500' : ''}`}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 left-0 pl-3 flex items-center"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="mt-2 text-sm text-red-600">{errors.confirmPassword.message}</p>
                  )}
                </div>
              </div>

              <div className="flex items-start space-x-3 space-x-reverse">
                <input
                  id="terms"
                  type="checkbox"
                  {...register('terms')}
                  className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                />
                <Label htmlFor="terms" className="block text-sm text-gray-700 leading-relaxed">
                  أوافق على{' '}
                  <Link href="/terms" className="text-blue-600 hover:text-blue-500 underline">
                    الشروط والأحكام
                  </Link>{' '}
                  و{' '}
                  <Link href="/privacy" className="text-blue-600 hover:text-blue-500 underline">
                    سياسة الخصوصية
                  </Link>{' '}
                  الخاصة بـ OZOO
                </Label>
              </div>
              {errors.terms && (
                <p className="mt-2 text-sm text-red-600">{errors.terms.message}</p>
              )}

              <div className="text-center pt-4">
                <Button
                  type="submit"
                  className={`w-full py-4 text-lg font-semibold rounded-lg transition-all duration-200 ${
                    !watch('terms')
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                  }`}
                  style={watch('terms') ? { backgroundColor: '#2563eb' } : {}}
                  disabled={isSubmitting || !watch('terms')}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="ml-2 h-5 w-5 animate-spin" />
                      جاري إنشاء الحساب...
                    </>
                  ) : (
                    'إنشاء حساب جديد في OZOO'
                  )}
                </Button>
                {!watch('terms') && (
                  <p className="mt-3 text-sm text-gray-500">
                    يرجى الموافقة على الشروط والأحكام لإنشاء الحساب
                  </p>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
