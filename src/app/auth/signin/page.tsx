'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { signInSchema, type SignInFormData } from '@/lib/validations/auth'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Alert } from '@/components/ui/Alert'
import { Loader2, Eye, EyeOff } from 'lucide-react'

export default function SignInPage() {
  // يجب أن تكون جميع الـ hooks في البداية وبنفس الترتيب دائماً
  const router = useRouter()
  const searchParams = useSearchParams()

  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const [isRedirecting, setIsRedirecting] = useState(false)

  // useForm يجب أن يكون مع باقي الـ hooks
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema)
  })

  const redirectTo = searchParams.get('redirectTo') || '/dashboard'

  // التحقق من حالة المستخدم باستخدام useSession
  useEffect(() => {
    const checkUserSession = async () => {
      try {
        setIsCheckingAuth(true)
        setError(null)

        // الحصول على الجلسة الحالية
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error('Session error:', sessionError)
          setError('حدث خطأ في التحقق من الجلسة')
          setIsCheckingAuth(false)
          return
        }

        // إذا لم تكن هناك جلسة، ابق في صفحة تسجيل الدخول
        if (!session || !session.user) {
          console.log('No active session found')
          setIsCheckingAuth(false)
          return
        }

        console.log('Active session found for user:', session.user.email)

        // التحقق من وجود مؤسسة في جدول profiles
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('organization_id')
          .eq('id', session.user.id)
          .single()

        if (profileError) {
          console.error('Profile fetch error:', profileError)

          // إذا لم يوجد ملف شخصي، وجه إلى setup-organization
          if (profileError.code === 'PGRST116') {
            console.log('No profile found, redirecting to setup-organization')
            setIsRedirecting(true)
            router.push('/setup-organization')
            return
          }

          setError('حدث خطأ في جلب بيانات الملف الشخصي')
          setIsCheckingAuth(false)
          return
        }

        // التحقق من وجود organization_id
        if (!profileData.organization_id) {
          console.log('No organization found, redirecting to setup-organization')
          setIsRedirecting(true)
          router.push('/setup-organization')
          return
        }

        // المستخدم لديه مؤسسة، وجه إلى dashboard
        console.log('Organization found, redirecting to dashboard')
        setIsRedirecting(true)
        router.push('/dashboard')

      } catch (error) {
        console.error('Unexpected error during auth check:', error)
        setError('حدث خطأ غير متوقع')
        setIsCheckingAuth(false)
      }
    }

    checkUserSession()
  }, [router])

  // إظهار شاشة تحميل أثناء فحص حالة المصادقة أو التوجيه
  if (isCheckingAuth || isRedirecting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {isCheckingAuth ? 'جاري التحقق من المصادقة...' : 'جاري التوجيه...'}
          </h2>
          <p className="text-gray-600">
            {isCheckingAuth
              ? 'يتم التحقق من حالة تسجيل الدخول والمؤسسة'
              : 'سيتم توجيهك إلى الصفحة المناسبة'
            }
          </p>
        </div>
      </div>
    )
  }

  const onSubmit = async (data: SignInFormData) => {
    try {
      setError(null)

      // تسجيل الدخول باستخدام Supabase مباشرة
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password
      })

      if (authError) {
        console.error('Sign in error:', authError)
        setError(authError.message || 'حدث خطأ أثناء تسجيل الدخول')
        return
      }

      if (!authData.user) {
        setError('فشل في تسجيل الدخول')
        return
      }

      console.log('تم تسجيل الدخول بنجاح:', authData.user.email)

      // بعد تسجيل الدخول بنجاح، تحقق من المؤسسة
      setIsCheckingAuth(true)

      // التحقق من وجود مؤسسة في جدول profiles
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('organization_id')
        .eq('id', authData.user.id)
        .single()

      if (profileError) {
        console.error('Profile fetch error after signin:', profileError)

        // إذا لم يوجد ملف شخصي، وجه إلى setup-organization
        if (profileError.code === 'PGRST116') {
          console.log('No profile found after signin, redirecting to setup-organization')
          setIsRedirecting(true)
          router.push('/setup-organization')
          return
        }

        setError('حدث خطأ في جلب بيانات الملف الشخصي')
        setIsCheckingAuth(false)
        return
      }

      // التحقق من وجود organization_id
      if (!profileData.organization_id) {
        console.log('No organization found after signin, redirecting to setup-organization')
        setIsRedirecting(true)
        router.push('/setup-organization')
        return
      }

      // المستخدم لديه مؤسسة، وجه إلى dashboard
      console.log('Organization found after signin, redirecting to dashboard')
      setIsRedirecting(true)
      router.push('/dashboard')

    } catch (err) {
      console.error('Unexpected sign in error:', err)
      setError('حدث خطأ غير متوقع')
      setIsCheckingAuth(false)
    }
  }



  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <span className="text-2xl font-bold text-blue-600">O</span>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            تسجيل الدخول إلى OZOO
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            أو{' '}
            <Link
              href="/auth/signup"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              إنشاء حساب جديد
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          {error && (
            <Alert variant="destructive">
              {error}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                type="email"
                autoComplete="email"
                {...register('email')}
                className={`bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500 ${errors.email ? 'border-red-500' : ''}`}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="password">كلمة المرور</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  {...register('password')}
                  className={`bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500 ${errors.password ? 'border-red-500' : ''}`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 left-0 pl-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember"
                type="checkbox"
                {...register('remember')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <Label htmlFor="remember" className="mr-2 block text-sm text-gray-900">
                تذكرني
              </Label>
            </div>

            <div className="text-sm">
              <Link
                href="/auth/forgot-password"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                نسيت كلمة المرور؟
              </Link>
            </div>
          </div>

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري تسجيل الدخول...
                </>
              ) : (
                'تسجيل الدخول'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
