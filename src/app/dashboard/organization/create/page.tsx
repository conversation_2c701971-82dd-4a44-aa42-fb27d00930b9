'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Building2, Loader2, Upload } from 'lucide-react'

// Schema للتحقق من صحة البيانات
const organizationSchema = z.object({
  name: z.string().min(2, 'اسم المؤسسة يجب أن يكون على الأقل حرفين'),
  business_type: z.string().min(1, 'يرجى اختيار نوع النشاط'),
  email: z.string().email('يرجى إدخال بريد إلكتروني صحيح').optional().or(z.literal('')),
  commercial_register: z.string().optional(),
  address: z.string().optional(),
  registration_date: z.string().optional(),
  entity_type: z.string().optional(),
  admin_phone: z.string().optional(),
  admin_email: z.string().email('يرجى إدخال بريد إلكتروني صحيح').optional().or(z.literal('')),
})

type OrganizationFormData = z.infer<typeof organizationSchema>

const businessTypes = [
  { value: 'technology', label: 'تقنية المعلومات' },
  { value: 'retail', label: 'تجارة التجزئة' },
  { value: 'manufacturing', label: 'التصنيع' },
  { value: 'services', label: 'الخدمات' },
  { value: 'healthcare', label: 'الرعاية الصحية' },
  { value: 'education', label: 'التعليم' },
  { value: 'construction', label: 'البناء والتشييد' },
  { value: 'finance', label: 'الخدمات المالية' },
  { value: 'food', label: 'الأغذية والمشروبات' },
  { value: 'transportation', label: 'النقل والمواصلات' },
  { value: 'real_estate', label: 'العقارات' },
  { value: 'consulting', label: 'الاستشارات' },
  { value: 'other', label: 'أخرى' },
]

const entityTypes = [
  { value: 'sole_proprietorship', label: 'مؤسسة فردية' },
  { value: 'limited_liability', label: 'شركة ذات مسؤولية محدودة' },
  { value: 'joint_stock', label: 'شركة مساهمة' },
  { value: 'partnership', label: 'شركة تضامن' },
  { value: 'branch', label: 'فرع شركة أجنبية' },
  { value: 'other', label: 'أخرى' },
]

export default function CreateOrganizationPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema)
  })

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      setLoading(true)
      setError(null)

      console.log('Creating organization with data:', data)

      // التحقق من المصادقة
      const { data: { user }, error: userError } = await supabase.auth.getUser()

      if (userError || !user) {
        throw new Error('يجب تسجيل الدخول أولاً')
      }

      console.log('User authenticated:', user.id)

      // إنشاء المؤسسة
      const { data: organizationData, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: data.name,
          business_type: data.business_type,
          email: data.email || null,
          commercial_register: data.commercial_register || null,
          address: data.address || null,
          registration_date: data.registration_date ? new Date(data.registration_date).toISOString().split('T')[0] : null,
          entity_type: data.entity_type || null,
          admin_phone: data.admin_phone || null,
          admin_email: data.admin_email || null,
          owner_id: user.id,
        })
        .select()
        .single()

      if (orgError) {
        console.error('Organization creation error details:', orgError)
        console.error('Full error object:', JSON.stringify(orgError, null, 2))
        throw new Error(`حدث خطأ في إنشاء المؤسسة: ${orgError.message}`)
      }

      if (!organizationData) {
        throw new Error('لم يتم إرجاع بيانات المؤسسة')
      }

      console.log('Organization created successfully:', organizationData)

      // تحديث الملف الشخصي للمستخدم
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ organization_id: organizationData.id })
        .eq('id', user.id)

      if (profileError) {
        console.error('Profile update error:', profileError)
        // لا نرمي خطأ هنا، المؤسسة تم إنشاؤها بنجاح
        console.warn('Organization created but profile update failed:', profileError.message)
      } else {
        console.log('Profile updated successfully with organization_id:', organizationData.id)
      }

      setSuccess(true)

      // توجيه إلى Dashboard بعد 2 ثانية
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)

    } catch (err) {
      console.error('Organization creation error:', err)
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100 mb-6">
            <Building2 className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            تم إنشاء المؤسسة بنجاح! 🎉
          </h2>
          <p className="text-gray-600 mb-6">
            سيتم توجيهك إلى لوحة التحكم خلال لحظات...
          </p>
          <div className="flex items-center justify-center">
            <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center text-gray-600 hover:text-gray-900">
                <ArrowLeft className="h-5 w-5 ml-2" />
                العودة إلى لوحة التحكم
              </Link>
            </div>
            <div className="flex items-center">
              <div className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-600">
                <span className="text-white font-bold">O</span>
              </div>
              <div className="mr-3">
                <h1 className="text-lg font-semibold text-gray-900">OZOO</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            إنشاء مؤسسة جديدة
          </h1>
          <p className="text-gray-600">
            أدخل بيانات مؤسستك للبدء في استخدام نظام OZOO المحاسبي
          </p>
        </div>

        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="h-5 w-5 ml-2" />
              بيانات المؤسسة
            </CardTitle>
            <CardDescription>
              املأ البيانات الأساسية لمؤسستك. يمكنك تعديل هذه البيانات لاحقاً من إعدادات المؤسسة.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* اسم المؤسسة */}
              <div>
                <Label htmlFor="name">اسم المؤسسة *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="مثال: شركة التقنية المتقدمة"
                  className="mt-1"
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>

              {/* نوع النشاط */}
              <div>
                <Label htmlFor="business_type">نوع النشاط *</Label>
                <Select onValueChange={(value) => setValue('business_type', value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="اختر نوع النشاط" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.business_type && (
                  <p className="text-sm text-red-600 mt-1">{errors.business_type.message}</p>
                )}
              </div>

              {/* البريد الإلكتروني */}
              <div>
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  placeholder="<EMAIL>"
                  className="mt-1"
                />
                {errors.email && (
                  <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
                )}
              </div>

              {/* السجل التجاري */}
              <div>
                <Label htmlFor="commercial_register">رقم السجل التجاري</Label>
                <Input
                  id="commercial_register"
                  {...register('commercial_register')}
                  placeholder="1010123456"
                  className="mt-1"
                />
              </div>

              {/* نوع الكيان */}
              <div>
                <Label htmlFor="entity_type">نوع الكيان القانوني</Label>
                <Select onValueChange={(value) => setValue('entity_type', value)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="اختر نوع الكيان" />
                  </SelectTrigger>
                  <SelectContent>
                    {entityTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* العنوان */}
              <div>
                <Label htmlFor="address">العنوان</Label>
                <Textarea
                  id="address"
                  {...register('address')}
                  placeholder="العنوان الكامل للمؤسسة"
                  className="mt-1"
                  rows={3}
                />
              </div>

              {/* تاريخ التسجيل */}
              <div>
                <Label htmlFor="registration_date">تاريخ التسجيل</Label>
                <Input
                  id="registration_date"
                  type="date"
                  {...register('registration_date')}
                  className="mt-1"
                />
              </div>

              {/* هاتف المسؤول */}
              <div>
                <Label htmlFor="admin_phone">هاتف المسؤول</Label>
                <Input
                  id="admin_phone"
                  {...register('admin_phone')}
                  placeholder="+966501234567"
                  className="mt-1"
                />
              </div>

              {/* بريد المسؤول */}
              <div>
                <Label htmlFor="admin_email">بريد المسؤول الإلكتروني</Label>
                <Input
                  id="admin_email"
                  type="email"
                  {...register('admin_email')}
                  placeholder="<EMAIL>"
                  className="mt-1"
                />
                {errors.admin_email && (
                  <p className="text-sm text-red-600 mt-1">{errors.admin_email.message}</p>
                )}
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex justify-between pt-6">
                <Link href="/dashboard">
                  <Button type="button" variant="outline">
                    إلغاء
                  </Button>
                </Link>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                      جاري الإنشاء...
                    </>
                  ) : (
                    <>
                      <Building2 className="h-4 w-4 ml-2" />
                      إنشاء المؤسسة
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
