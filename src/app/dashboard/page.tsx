'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { supabase } from '@/lib/supabase'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  BarChart3,
  Users,
  FileText,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Building2,
  Plus,
  Settings,
  Loader2
} from 'lucide-react'

interface UserProfile {
  id: string
  organization_id: string | null
}

interface Organization {
  id: string
  name: string
  business_type: string | null
  logo_url: string | null
  created_at: string
}

export default function DashboardPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [organization, setOrganization] = useState<Organization | null>(null)

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        setLoading(true)
        setError(null)

        // التحقق من المصادقة
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError || !session?.user) {
          console.log('No active session, redirecting to signin')
          router.push('/auth/signin')
          return
        }

        console.log('User authenticated:', session.user.email)

        // جلب الملف الشخصي للمستخدم
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id, organization_id')
          .eq('id', session.user.id)
          .single()

        if (profileError) {
          if (profileError.code === 'PGRST116') {
            // إنشاء ملف شخصي جديد
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: session.user.id,
                organization_id: null
              })

            if (createError) {
              throw new Error(`حدث خطأ في إنشاء الملف الشخصي: ${createError.message}`)
            }

            setUserProfile({ id: session.user.id, organization_id: null })
          } else {
            throw new Error(`حدث خطأ في جلب الملف الشخصي: ${profileError.message}`)
          }
        } else {
          setUserProfile(profileData)

          // إذا كان لدى المستخدم مؤسسة، جلب بياناتها
          if (profileData.organization_id) {
            const { data: orgData, error: orgError } = await supabase
              .from('organizations')
              .select('id, name, business_type, logo_url, created_at')
              .eq('id', profileData.organization_id)
              .single()

            if (orgError) {
              console.error('Organization fetch error:', orgError)
              // لا نرمي خطأ هنا، فقط نسجل المشكلة
            } else {
              setOrganization(orgData)
            }
          }
        }

      } catch (error) {
        console.error('Dashboard load error:', error)
        setError(error instanceof Error ? error.message : 'حدث خطأ غير متوقع')
      } finally {
        setLoading(false)
      }
    }

    checkAuthAndLoadData()
  }, [router])

  const stats = [
    {
      title: 'إجمالي الإيرادات',
      value: '125,430 ر.س',
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign
    },
    {
      title: 'إجمالي المصروفات',
      value: '89,240 ر.س',
      change: '-3.2%',
      trend: 'down',
      icon: TrendingDown
    },
    {
      title: 'صافي الربح',
      value: '36,190 ر.س',
      change: '+18.7%',
      trend: 'up',
      icon: TrendingUp
    },
    {
      title: 'عدد العملاء',
      value: '1,234',
      change: '+5.4%',
      trend: 'up',
      icon: Users
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'invoice',
      description: 'تم إنشاء فاتورة جديدة #INV-001',
      time: 'منذ 5 دقائق',
      amount: '2,500 ر.س'
    },
    {
      id: 2,
      type: 'payment',
      description: 'تم استلام دفعة من العميل أحمد محمد',
      time: 'منذ 15 دقيقة',
      amount: '5,000 ر.س'
    },
    {
      id: 3,
      type: 'expense',
      description: 'تم تسجيل مصروف جديد - إيجار المكتب',
      time: 'منذ ساعة',
      amount: '8,000 ر.س'
    },
    {
      id: 4,
      type: 'customer',
      description: 'تم إضافة عميل جديد - شركة النور للتجارة',
      time: 'منذ ساعتين',
      amount: null
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            جاري تحميل لوحة التحكم...
          </h2>
          <p className="text-gray-600">
            يتم تحميل بياناتك
          </p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100 mb-4">
            <span className="text-2xl">⚠️</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            حدث خطأ
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            إعادة المحاولة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-600">
                  <span className="text-white font-bold">O</span>
                </div>
              </div>
              <div className="mr-4">
                <h1 className="text-xl font-semibold text-gray-900">OZOO</h1>
                <p className="text-sm text-gray-500">نظام المحاسبة الذكي</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={async () => {
                  await supabase.auth.signOut()
                  router.push('/auth/signin')
                }}
              >
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            مرحباً بك في لوحة التحكم
          </h2>
          <p className="text-gray-600">
            إدارة شاملة لأعمالك المحاسبية والمالية
          </p>
        </div>

        {/* Organization Status */}
        {!userProfile?.organization_id ? (
          <Alert className="mb-8 border-blue-200 bg-blue-50">
            <Building2 className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium mb-1">لم يتم إنشاء مؤسسة بعد</p>
                  <p className="text-sm">الرجاء إنشاء مؤسسة للاستفادة من جميع ميزات النظام</p>
                </div>
                <Link href="/dashboard/organization/create">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="h-4 w-4 ml-2" />
                    إنشاء مؤسسة
                  </Button>
                </Link>
              </div>
            </AlertDescription>
          </Alert>
        ) : (
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Building2 className="h-5 w-5 text-green-600 ml-2" />
                  <div>
                    <CardTitle className="text-green-800">
                      {organization?.name || 'مؤسستك'}
                    </CardTitle>
                    <CardDescription className="text-green-600">
                      {organization?.business_type || 'نشاط تجاري'}
                    </CardDescription>
                  </div>
                </div>
                <Link href="/dashboard/organization/settings">
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 ml-2" />
                    إعدادات المؤسسة
                  </Button>
                </Link>
              </div>
            </CardHeader>
          </Card>
        )}

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <FileText className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <CardTitle>الفواتير</CardTitle>
              <CardDescription>إنشاء وإدارة الفواتير</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" disabled={!userProfile?.organization_id}>
                {userProfile?.organization_id ? 'إدارة الفواتير' : 'يتطلب إنشاء مؤسسة'}
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <CardTitle>العملاء</CardTitle>
              <CardDescription>إدارة بيانات العملاء</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" disabled={!userProfile?.organization_id}>
                {userProfile?.organization_id ? 'إدارة العملاء' : 'يتطلب إنشاء مؤسسة'}
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="text-center">
              <BarChart3 className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <CardTitle>التقارير</CardTitle>
              <CardDescription>تقارير مالية ومحاسبية</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" disabled={!userProfile?.organization_id}>
                {userProfile?.organization_id ? 'عرض التقارير' : 'يتطلب إنشاء مؤسسة'}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Stats Grid - Only show if user has organization */}
        {userProfile?.organization_id && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-full ${
                    stat.trend === 'up' ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <stat.icon className={`h-6 w-6 ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`} />
                  </div>
                </div>
                <div className="mt-4">
                  <span className={`text-sm font-medium ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>النشاط الأخير</CardTitle>
            <CardDescription>آخر العمليات والأنشطة</CardDescription>
          </CardHeader>
          <CardContent>
            {userProfile?.organization_id ? (
              <div className="text-center py-8 text-gray-500">
                <p>لا توجد أنشطة حديثة</p>
                <p className="text-sm">ابدأ بإنشاء فاتورة أو إضافة عميل جديد</p>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Building2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>قم بإنشاء مؤسسة أولاً لرؤية النشاطات</p>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
