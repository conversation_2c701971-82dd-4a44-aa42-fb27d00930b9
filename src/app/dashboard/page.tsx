'use client'

import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { Button } from '@/components/ui/Button'
import { OrganizationGuard } from '@/components/guards/OrganizationGuard'
import { useAuth } from '@/contexts/AuthContext'
import {
  BarChart3,
  Users,
  FileText,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar
} from 'lucide-react'

export default function DashboardPage() {
  const { user } = useAuth()

  const stats = [
    {
      title: 'إجمالي الإيرادات',
      value: '125,430 ر.س',
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign
    },
    {
      title: 'إجمالي المصروفات',
      value: '89,240 ر.س',
      change: '-3.2%',
      trend: 'down',
      icon: TrendingDown
    },
    {
      title: 'صافي الربح',
      value: '36,190 ر.س',
      change: '+18.7%',
      trend: 'up',
      icon: TrendingUp
    },
    {
      title: 'عدد العملاء',
      value: '1,234',
      change: '+5.4%',
      trend: 'up',
      icon: Users
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'invoice',
      description: 'تم إنشاء فاتورة جديدة #INV-001',
      time: 'منذ 5 دقائق',
      amount: '2,500 ر.س'
    },
    {
      id: 2,
      type: 'payment',
      description: 'تم استلام دفعة من العميل أحمد محمد',
      time: 'منذ 15 دقيقة',
      amount: '5,000 ر.س'
    },
    {
      id: 3,
      type: 'expense',
      description: 'تم تسجيل مصروف جديد - إيجار المكتب',
      time: 'منذ ساعة',
      amount: '8,000 ر.س'
    },
    {
      id: 4,
      type: 'customer',
      description: 'تم إضافة عميل جديد - شركة النور للتجارة',
      time: 'منذ ساعتين',
      amount: null
    }
  ]

  return (
    <OrganizationGuard>
      <DashboardLayout>
      {/* Welcome Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          مرحباً، {user?.full_name || 'المستخدم'}
        </h2>
        <p className="text-gray-600">
          إليك نظرة عامة على أداء {user?.organization?.name || 'مؤسستك'} اليوم
        </p>
        {user?.organization && (
          <div className="mt-2 text-sm text-gray-500">
            المؤسسة: {user.organization.name}
            {user.organization.country && ` • ${user.organization.country}`}
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-full ${
                stat.trend === 'up' ? 'bg-green-100' : 'bg-red-100'
              }`}>
                <stat.icon className={`h-6 w-6 ${
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`} />
              </div>
            </div>
            <div className="mt-4">
              <span className={`text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change}
              </span>
              <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
            </div>
          </div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activities */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">النشاطات الأخيرة</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center">
                      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {activity.description}
                        </p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                    {activity.amount && (
                      <span className="text-sm font-medium text-gray-900">
                        {activity.amount}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">إجراءات سريعة</h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="h-4 w-4 ml-2" />
                  إنشاء فاتورة جديدة
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 ml-2" />
                  إضافة عميل جديد
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <DollarSign className="h-4 w-4 ml-2" />
                  تسجيل مصروف
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="h-4 w-4 ml-2" />
                  عرض التقارير
                </Button>
              </div>
            </div>
          </div>

          {/* Calendar Widget */}
          <div className="bg-white rounded-lg shadow mt-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">التقويم</h3>
            </div>
            <div className="p-6">
              <div className="flex items-center justify-center h-32 bg-gray-50 rounded-lg">
                <Calendar className="h-8 w-8 text-gray-400" />
                <span className="text-gray-500 mr-2">قريباً</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      </DashboardLayout>
    </OrganizationGuard>
  )
}
