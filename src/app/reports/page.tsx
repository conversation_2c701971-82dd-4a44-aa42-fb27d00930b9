'use client'

import { OrganizationGuard } from '@/components/guards/OrganizationGuard'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { 
  BarChart3, 
  FileText, 
  Download, 
  Calendar,
  TrendingUp,
  DollarSign,
  Users,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'

export default function ReportsPage() {
  return (
    <OrganizationGuard>
      <ReportsContent />
    </OrganizationGuard>
  )
}

function ReportsContent() {
  const { user } = useAuth()

  const reportTypes = [
    {
      id: 'financial',
      title: 'التقارير المالية',
      description: 'تقارير الإيرادات والمصروفات والأرباح',
      icon: DollarSign,
      color: 'bg-green-100 text-green-600',
      reports: [
        'تقرير الأرباح والخسائر',
        'تقرير المركز المالي',
        'تقرير التدفقات النقدية',
        'تقرير الميزانية العمومية'
      ]
    },
    {
      id: 'sales',
      title: 'تقارير المبيعات',
      description: 'تقارير المبيعات والعملاء والفواتير',
      icon: TrendingUp,
      color: 'bg-blue-100 text-blue-600',
      reports: [
        'تقرير المبيعات الشهرية',
        'تقرير أداء العملاء',
        'تقرير الفواتير المستحقة',
        'تقرير المنتجات الأكثر مبيعاً'
      ]
    },
    {
      id: 'customers',
      title: 'تقارير العملاء',
      description: 'تقارير العملاء والحسابات المدينة',
      icon: Users,
      color: 'bg-purple-100 text-purple-600',
      reports: [
        'تقرير العملاء الجدد',
        'تقرير الحسابات المدينة',
        'تقرير أعمار الديون',
        'تقرير نشاط العملاء'
      ]
    },
    {
      id: 'tax',
      title: 'التقارير الضريبية',
      description: 'تقارير ضريبة القيمة المضافة والزكاة',
      icon: FileText,
      color: 'bg-orange-100 text-orange-600',
      reports: [
        'تقرير ضريبة القيمة المضافة',
        'تقرير الزكاة',
        'تقرير الضرائب المستحقة',
        'تقرير الإقرارات الضريبية'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/dashboard"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 ml-2" />
                العودة إلى لوحة التحكم
              </Link>
            </div>
            <div className="flex items-center">
              <BarChart3 className="h-6 w-6 text-blue-600 ml-3" />
              <h1 className="text-2xl font-bold text-gray-900">التقارير</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            تقارير {user?.organization?.name || 'المؤسسة'}
          </h2>
          <p className="text-gray-600">
            اختر نوع التقرير الذي تريد إنشاؤه أو عرضه
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-gray-900">125,430 ر.س</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">نمو المبيعات</p>
                <p className="text-2xl font-bold text-gray-900">+12.5%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد العملاء</p>
                <p className="text-2xl font-bold text-gray-900">1,234</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100">
                <FileText className="h-6 w-6 text-orange-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الفواتير</p>
                <p className="text-2xl font-bold text-gray-900">456</p>
              </div>
            </div>
          </div>
        </div>

        {/* Report Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {reportTypes.map((reportType) => (
            <div key={reportType.id} className="bg-white rounded-lg shadow-md">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className={`p-3 rounded-full ${reportType.color}`}>
                    <reportType.icon className="h-6 w-6" />
                  </div>
                  <div className="mr-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {reportType.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {reportType.description}
                    </p>
                  </div>
                </div>

                <div className="space-y-2 mb-6">
                  {reportType.reports.map((report, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-sm text-gray-700">{report}</span>
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 ml-2" />
                        تحميل
                      </Button>
                    </div>
                  ))}
                </div>

                <div className="flex space-x-3 space-x-reverse">
                  <Button className="flex-1">
                    <BarChart3 className="h-4 w-4 ml-2" />
                    عرض التقارير
                  </Button>
                  <Button variant="outline">
                    <Calendar className="h-4 w-4 ml-2" />
                    تخصيص الفترة
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Coming Soon Section */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-8 text-center">
          <BarChart3 className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            المزيد من التقارير قريباً
          </h3>
          <p className="text-gray-600 mb-6">
            نعمل على إضافة المزيد من التقارير المتقدمة والتحليلات التفاعلية
          </p>
          <Button variant="outline">
            <FileText className="h-4 w-4 ml-2" />
            طلب تقرير مخصص
          </Button>
        </div>
      </div>
    </div>
  )
}
