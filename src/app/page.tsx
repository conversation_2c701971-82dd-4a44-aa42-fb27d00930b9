'use client'

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { supabase } from "@/lib/supabase/client";
import {
  BarChart3,
  Users,
  FileText,
  DollarSign,
  Shield,
  Globe,
  Zap,
  CheckCircle,
  Loader2,
  AlertTriangle,
  RefreshCw
} from "lucide-react";

export default function Home() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showContent, setShowContent] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hasCheckedSession = useRef(false)

  // فحص الجلسة مع timeout
  useEffect(() => {
    // منع تشغيل useEffect مرتين
    if (hasCheckedSession.current) return
    hasCheckedSession.current = true

    const checkSession = async () => {
      try {
        setIsLoading(true)
        setError(null)

        console.log('Checking session...')

        // إعداد timeout لمدة 7 ثوان
        timeoutRef.current = setTimeout(() => {
          setError('انتهت مهلة التحقق من المصادقة')
          setIsLoading(false)
        }, 7000)

        // التحقق من الجلسة
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        // إلغاء timeout إذا تم الحصول على النتيجة
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
          timeoutRef.current = null
        }

        if (sessionError) {
          console.error('Session error:', sessionError)
          setError('حدث خطأ في التحقق من المصادقة')
          setIsLoading(false)
          return
        }

        console.log('Session check result:', session ? 'Found session' : 'No session')

        if (session) {
          // إذا كانت الجلسة موجودة، وجه إلى dashboard
          console.log('Redirecting to dashboard...')
          router.push('/dashboard')
        } else {
          // إذا لم تكن هناك جلسة، أظهر المحتوى
          console.log('No session found, showing home content')
          setShowContent(true)
          setIsLoading(false)
        }
      } catch (err) {
        console.error('Unexpected error checking session:', err)

        // إلغاء timeout في حالة الخطأ
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
          timeoutRef.current = null
        }

        setError('حدث خطأ غير متوقع في التحقق من المصادقة')
        setIsLoading(false)
      }
    }

    checkSession()

    // تنظيف timeout عند إلغاء المكون
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [router])

  // دالة إعادة المحاولة
  const retryCheck = () => {
    hasCheckedSession.current = false
    setError(null)
    setShowContent(false)
    // إعادة تشغيل useEffect
    window.location.reload()
  }

  // شاشة التحميل
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center max-w-md">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            جاري التحقق من المصادقة...
          </h2>
          <p className="text-gray-600">
            يتم فحص حالة تسجيل الدخول وتوجيهك للصفحة المناسبة
          </p>
        </div>
      </div>
    )
  }

  // شاشة الخطأ
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center max-w-md">
          <AlertTriangle className="mx-auto h-16 w-16 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            خطأ في التحقق من المصادقة
          </h2>
          <p className="text-gray-600 mb-6">
            {error}. يرجى إعادة المحاولة.
          </p>
          <div className="space-y-3">
            <Button
              onClick={retryCheck}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              <RefreshCw className="h-5 w-5 ml-2" />
              إعادة المحاولة
            </Button>
            <Button
              onClick={() => router.push('/auth/signin')}
              variant="outline"
              className="w-full"
            >
              الذهاب لتسجيل الدخول
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // إذا لم يتم عرض المحتوى بعد، أظهر شاشة انتظار
  if (!showContent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 text-blue-600 animate-spin mb-4" />
          <p className="text-gray-600">جاري التوجيه...</p>
        </div>
      </div>
    )
  }

  const features = [
    {
      icon: BarChart3,
      title: "تقارير مالية متقدمة",
      description: "احصل على تقارير مالية شاملة ومفصلة لمتابعة أداء مؤسستك"
    },
    {
      icon: Shield,
      title: "أمان عالي المستوى",
      description: "حماية بيانات مؤسستك بأعلى معايير الأمان والخصوصية"
    },
    {
      icon: Globe,
      title: "متوافق مع زاتكا",
      description: "نظام متوافق مع متطلبات هيئة الزكاة والضريبة والجمارك السعودية"
    },
    {
      icon: Zap,
      title: "سرعة وكفاءة",
      description: "نظام سريع وموثوق يوفر الوقت والجهد في إدارة الحسابات"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">O</span>
              </div>
              <span className="mr-3 text-xl font-bold text-gray-900">OZOO</span>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <Link href="/auth/signin">
                <Button variant="ghost">تسجيل الدخول</Button>
              </Link>
              <Link href="/auth/signup">
                <Button>إنشاء حساب</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            نظام محاسبي متكامل
            <span className="block text-blue-600">للشركات الحديثة</span>
          </h1>

          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            OZOO هو نظام محاسبي سحابي متكامل مصمم خصيصاً للشركات والمؤسسات في المملكة العربية السعودية ودول الخليج.
            يوفر جميع الأدوات اللازمة لإدارة الحسابات والفوترة الإلكترونية بكفاءة عالية.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Link href="/auth/signup">
              <Button size="lg" className="w-full sm:w-auto">
                ابدأ تجربتك المجانية
              </Button>
            </Link>
            <Link href="/auth/signin">
              <Button variant="outline" size="lg" className="w-full sm:w-auto bg-white text-blue-600 border-blue-600 hover:bg-blue-50">
                تسجيل الدخول
              </Button>
            </Link>
            <Link href="/pricing">
              <Button variant="ghost" size="lg" className="w-full sm:w-auto text-gray-600 hover:text-gray-900">
                عرض الخطط والأسعار
              </Button>
            </Link>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          {/* Benefits Section */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              لماذا تختار OZOO؟
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1 ml-3" />
                  <div>
                    <h4 className="font-semibold text-gray-900">سهولة الاستخدام</h4>
                    <p className="text-gray-600 text-sm">واجهة بسيطة ومفهومة تناسب جميع المستخدمين</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1 ml-3" />
                  <div>
                    <h4 className="font-semibold text-gray-900">دعم فني متميز</h4>
                    <p className="text-gray-600 text-sm">فريق دعم فني متخصص متاح على مدار الساعة</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1 ml-3" />
                  <div>
                    <h4 className="font-semibold text-gray-900">تحديثات مستمرة</h4>
                    <p className="text-gray-600 text-sm">تحديثات دورية لضمان مواكبة أحدث المتطلبات</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1 ml-3" />
                  <div>
                    <h4 className="font-semibold text-gray-900">نسخ احتياطية آمنة</h4>
                    <p className="text-gray-600 text-sm">حفظ تلقائي ونسخ احتياطية لضمان أمان البيانات</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1 ml-3" />
                  <div>
                    <h4 className="font-semibold text-gray-900">تكامل مع الأنظمة</h4>
                    <p className="text-gray-600 text-sm">تكامل سلس مع الأنظمة الحكومية والبنكية</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1 ml-3" />
                  <div>
                    <h4 className="font-semibold text-gray-900">أسعار تنافسية</h4>
                    <p className="text-gray-600 text-sm">خطط مرنة وأسعار مناسبة لجميع أحجام الأعمال</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action Section */}
          <div className="mt-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl shadow-xl p-8 md:p-12 text-center text-white">
            <h2 className="text-3xl font-bold mb-4">
              جاهز لتحويل إدارة حساباتك؟
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              انضم إلى آلاف الشركات التي تثق في OZOO لإدارة حساباتها بكفاءة وأمان
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/signup">
                <Button size="lg" className="w-full sm:w-auto bg-white text-blue-600 hover:bg-gray-100">
                  ابدأ الآن مجاناً
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-blue-600">
                  تسجيل الدخول
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">O</span>
              </div>
              <span className="mr-3 text-xl font-bold">OZOO</span>
            </div>
            <p className="text-gray-400 mb-4">
              نظام محاسبي متكامل للشركات الحديثة
            </p>
            <p className="text-gray-500 text-sm">
              © 2024 OZOO. جميع الحقوق محفوظة.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
