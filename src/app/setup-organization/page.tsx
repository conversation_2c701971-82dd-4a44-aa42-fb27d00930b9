'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase/client'
import {
  uploadFileWithBucketCreation,
  fileToBase64,
  validateFileType,
  validateFileSize,
  generateUniqueFileName,
  STORAGE_BUCKETS,
  FILE_SIZE_LIMITS,
  ALLOWED_IMAGE_TYPES
} from '@/lib/supabase/setup'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Alert } from '@/components/ui/Alert'
import { Loader2, Building2, CheckCircle, Upload, X } from 'lucide-react'

// نموذج محدث يحتوي على جميع الحقول المطلوبة
const organizationSchema = z.object({
  name: z.string().min(2, 'اسم المؤسسة يجب أن يكون حرفين على الأقل').max(100, 'اسم المؤسسة طويل جداً'),
  business_type: z.string().optional(),
  logo_url: z.string().optional(),
})

type OrganizationFormData = z.infer<typeof organizationSchema>

// خيارات النشاط التجاري
const businessTypes = [
  { value: '', label: 'اختر النشاط التجاري' },
  { value: 'retail', label: 'تجارة التجزئة' },
  { value: 'wholesale', label: 'تجارة الجملة' },
  { value: 'manufacturing', label: 'التصنيع' },
  { value: 'services', label: 'الخدمات' },
  { value: 'technology', label: 'التكنولوجيا' },
  { value: 'healthcare', label: 'الرعاية الصحية' },
  { value: 'education', label: 'التعليم' },
  { value: 'construction', label: 'البناء والتشييد' },
  { value: 'food_beverage', label: 'الأغذية والمشروبات' },
  { value: 'transportation', label: 'النقل والمواصلات' },
  { value: 'real_estate', label: 'العقارات' },
  { value: 'finance', label: 'الخدمات المالية' },
  { value: 'consulting', label: 'الاستشارات' },
  { value: 'other', label: 'أخرى' },
]

export default function SetupOrganizationPage() {
  // يجب أن تكون جميع الـ hooks في البداية وبنفس الترتيب دائماً
  const router = useRouter()
  const { user, loading } = useAuth()

  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [uploadingLogo, setUploadingLogo] = useState(false)
  const [checkingAuth, setCheckingAuth] = useState(true)

  // useForm يجب أن يكون مع باقي الـ hooks
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: user?.full_name ? `مؤسسة ${user.full_name}` : '',
      business_type: '',
      logo_url: ''
    }
  })

  // فحص حالة المصادقة والمؤسسة
  useEffect(() => {
    const checkAuthAndOrganization = async () => {
      // إذا كان لا يزال يحمل، انتظر
      if (loading) {
        return
      }

      // إذا لم يكن مسجل الدخول، وجهه لتسجيل الدخول
      if (!user) {
        router.push('/auth/signin')
        return
      }

      // إذا كان لديه مؤسسة بالفعل، وجهه للوحة التحكم
      if (user.organization) {
        router.push('/dashboard')
        return
      }

      // إذا وصل هنا، فهو مسجل الدخول وليس لديه مؤسسة - يمكن عرض النموذج
      setCheckingAuth(false)
    }

    checkAuthAndOrganization()
  }, [user, loading, router])

  // تحديث القيم الافتراضية عند تحميل بيانات المستخدم
  useEffect(() => {
    if (user?.full_name) {
      setValue('name', `مؤسسة ${user.full_name}`)
    }
  }, [user?.full_name, setValue])

  // دالة التعامل مع رفع الشعار
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // التحقق من نوع الملف
    if (!validateFileType(file, ALLOWED_IMAGE_TYPES)) {
      setError('يرجى اختيار ملف صورة صالح (PNG, JPG, GIF, WebP)')
      return
    }

    // التحقق من حجم الملف
    if (!validateFileSize(file, FILE_SIZE_LIMITS.LOGO)) {
      setError('حجم الصورة يجب أن يكون أقل من 2 ميجابايت')
      return
    }

    setError(null) // مسح أي أخطاء سابقة
    setLogoFile(file)

    // إنشاء معاينة للصورة
    try {
      const preview = await fileToBase64(file)
      if (preview) {
        setLogoPreview(preview)
      }
    } catch (error) {
      console.error('Error creating preview:', error)
      setError('حدث خطأ في معاينة الصورة')
    }
  }

  // دالة رفع الشعار إلى Supabase Storage
  const uploadLogo = async (): Promise<string | null> => {
    if (!logoFile || !user) return null

    try {
      setUploadingLogo(true)

      // إنشاء اسم فريد للملف
      const fileName = generateUniqueFileName(logoFile.name, user.id)

      // محاولة رفع الملف مع إنشاء الـ bucket إذا لم يكن موجوداً
      const { data, error } = await uploadFileWithBucketCreation(
        STORAGE_BUCKETS.ORGANIZATION_LOGOS,
        fileName,
        logoFile,
        {
          public: true,
          allowedMimeTypes: ALLOWED_IMAGE_TYPES,
          fileSizeLimit: FILE_SIZE_LIMITS.LOGO
        }
      )

      if (error) {
        console.error('Logo upload error:', error)
        // إذا فشل الرفع، نستخدم base64 كبديل
        const base64 = await fileToBase64(logoFile)
        return base64
      }

      // إرجاع URL العام للصورة
      return data?.publicUrl || null
    } catch (error) {
      console.error('Logo upload error:', error)
      // في حالة فشل كامل، نحاول استخدام base64
      try {
        const base64 = await fileToBase64(logoFile)
        return base64
      } catch {
        return null
      }
    } finally {
      setUploadingLogo(false)
    }
  }

  // دالة حذف الشعار
  const removeLogo = () => {
    setLogoFile(null)
    setLogoPreview(null)
    setValue('logo_url', '')
  }

  // إظهار شاشة التحميل أثناء التحقق من حالة المستخدم
  if (loading || checkingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            جاري التحقق من حالة المستخدم...
          </h2>
          <p className="text-gray-600">
            يتم التحقق من بيانات المصادقة والمؤسسة
          </p>
        </div>
      </div>
    )
  }

  // إذا لم يكن مسجل الدخول أو لديه مؤسسة، فسيتم التوجيه تلقائياً
  // هذا الشرط للأمان فقط - لا يجب أن يصل هنا
  if (!user || user.organization) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 text-blue-600 animate-spin mb-4" />
          <p className="text-gray-600">جاري التوجيه...</p>
        </div>
      </div>
    )
  }

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      setError(null)

      if (!user) {
        setError('يجب تسجيل الدخول أولاً')
        router.push('/auth/signin')
        return
      }

      // رفع الشعار إذا تم اختياره
      let logoUrl = null
      if (logoFile) {
        logoUrl = await uploadLogo()
        if (!logoUrl) {
          setError('حدث خطأ في رفع الشعار. يرجى المحاولة مرة أخرى.')
          return
        }
      }

      // إنشاء سجل جديد في جدول organizations
      const organizationData = {
        name: data.name,
        owner_id: user.id,
        created_by: user.id, // إضافة created_by كما هو مطلوب
        email: user.email,
        business_type: data.business_type || null,
        logo_url: logoUrl || data.logo_url || null,
        created_at: new Date().toISOString(),
      }

      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .insert(organizationData)
        .select()
        .single()

      if (orgError) {
        console.error('Organization creation error:', orgError)
        if (orgError.code === '23505') {
          setError('يبدو أن لديك مؤسسة بالفعل. سيتم توجيهك إلى لوحة التحكم.')
          setTimeout(() => router.push('/dashboard'), 2000)
        } else {
          setError('حدث خطأ في إنشاء المؤسسة. يرجى المحاولة مرة أخرى.')
        }
        return
      }

      // تحديث جدول profiles لربط organization_id بالمستخدم الحالي
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          organization_id: orgData.id
        })
        .eq('id', user.id)

      if (profileError) {
        console.error('Profile update error:', profileError)
        setError('حدث خطأ في ربط المؤسسة بحسابك. يرجى المحاولة مرة أخرى.')
        return
      }

      // إظهار رسالة النجاح
      setSuccess(true)

      // التوجيه إلى لوحة التحكم بعد ثانيتين
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)

    } catch (err) {
      console.error('Organization setup error:', err)
      setError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.')
    }
  }



  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-4 text-2xl font-bold text-gray-900">
            تم إعداد المؤسسة بنجاح!
          </h2>
          <p className="mt-2 text-gray-600">
            سيتم توجيهك إلى لوحة التحكم خلال ثوانٍ...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <Building2 className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            إعداد المؤسسة
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            أكمل إعداد مؤسستك للبدء في استخدام OZOO
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          {error && (
            <Alert variant="destructive">
              {error}
            </Alert>
          )}

          <div className="space-y-6">
            {/* اسم المؤسسة */}
            <div>
              <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                اسم المؤسسة *
              </Label>
              <Input
                id="name"
                type="text"
                {...register('name')}
                className={`w-full px-4 py-3 border-2 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="أدخل اسم المؤسسة"
                autoFocus
              />
              {errors.name && (
                <p className="mt-2 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            {/* النشاط التجاري */}
            <div>
              <Label htmlFor="business_type" className="block text-sm font-medium text-gray-700 mb-2">
                النشاط التجاري أو التصنيف (اختياري)
              </Label>
              <select
                id="business_type"
                {...register('business_type')}
                className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
              >
                {businessTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                اختر النشاط التجاري الذي يناسب مؤسستك
              </p>
            </div>

            {/* رفع الشعار */}
            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">
                شعار المؤسسة (اختياري)
              </Label>

              {!logoPreview ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                      اسحب وأفلت صورة الشعار هنا، أو
                    </p>
                    <label className="inline-block">
                      <span className="bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors">
                        اختر ملف
                      </span>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    PNG, JPG, GIF حتى 2MB
                  </p>
                </div>
              ) : (
                <div className="relative inline-block">
                  <img
                    src={logoPreview}
                    alt="معاينة الشعار"
                    className="w-32 h-32 object-cover rounded-lg border-2 border-gray-300"
                  />
                  <button
                    type="button"
                    onClick={removeLogo}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
          </div>

          <div>
            <Button
              type="submit"
              className="w-full py-4 text-lg font-semibold bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              disabled={isSubmitting || uploadingLogo}
              style={{ backgroundColor: '#2563eb' }}
            >
              {isSubmitting || uploadingLogo ? (
                <>
                  <Loader2 className="ml-2 h-5 w-5 animate-spin" />
                  {uploadingLogo ? 'جاري رفع الشعار...' : 'جاري إنشاء المؤسسة...'}
                </>
              ) : (
                'إنشاء المؤسسة والمتابعة إلى لوحة التحكم'
              )}
            </Button>
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              بإنشاء المؤسسة، ستتمكن من الوصول إلى جميع مميزات OZOO
            </p>
            <p className="text-xs text-gray-500">
              يمكنك تعديل هذه المعلومات لاحقاً من صفحة الإعدادات
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}
