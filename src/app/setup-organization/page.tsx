'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useSetupOrganizationProtection } from '@/hooks/useAuthGuard'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Alert } from '@/components/ui/Alert'
import { Loader2, Building2, CheckCircle } from 'lucide-react'

// نموذج بسيط يحتوي على حقل اسم المؤسسة فقط
const organizationSchema = z.object({
  name: z.string().min(2, 'اسم المؤسسة يجب أن يكون حرفين على الأقل').max(100, 'اسم المؤسسة طويل جداً'),
})

type OrganizationFormData = z.infer<typeof organizationSchema>

export default function SetupOrganizationPage() {
  // يجب أن تكون جميع الـ hooks في البداية وبنفس الترتيب دائماً
  const router = useRouter()
  const {
    user,
    isLoading,
    status,
    redirectToAppropriateRoute
  } = useSetupOrganizationProtection()

  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  // إظهار شاشة التحميل أثناء التحقق من حالة المستخدم
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900">جاري التحقق من حالة المستخدم...</h2>
        </div>
      </div>
    )
  }

  // إذا لم يكن في الحالة المناسبة، سيتم التوجيه تلقائياً
  if (status !== 'no-organization') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 text-blue-600 animate-spin mb-4" />
          <p className="text-gray-600">جاري التوجيه...</p>
        </div>
      </div>
    )
  }

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: user?.full_name ? `مؤسسة ${user.full_name}` : ''
    }
  })

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      setError(null)

      if (!user) {
        setError('يجب تسجيل الدخول أولاً')
        redirectToAppropriateRoute()
        return
      }

      // إنشاء سجل جديد في جدول organizations
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: data.name,
          owner_id: user.id,
          email: user.email // إضافة البريد الإلكتروني للبحث المستقبلي
        })
        .select()
        .single()

      if (orgError) {
        console.error('Organization creation error:', orgError)
        if (orgError.code === '23505') {
          setError('يبدو أن لديك مؤسسة بالفعل. سيتم توجيهك إلى لوحة التحكم.')
          setTimeout(() => router.push('/dashboard'), 2000)
        } else {
          setError('حدث خطأ في إنشاء المؤسسة. يرجى المحاولة مرة أخرى.')
        }
        return
      }

      // تحديث جدول profiles لربط organization_id بالمستخدم الحالي
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          organization_id: orgData.id
        })
        .eq('id', user.id)

      if (profileError) {
        console.error('Profile update error:', profileError)
        setError('حدث خطأ في ربط المؤسسة بحسابك. يرجى المحاولة مرة أخرى.')
        return
      }

      // إظهار رسالة النجاح
      setSuccess(true)

      // التوجيه إلى لوحة التحكم بعد ثانيتين
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)

    } catch (err) {
      console.error('Organization setup error:', err)
      setError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.')
    }
  }

  // إظهار شاشة التحميل أثناء التحقق من حالة المستخدم
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900">جاري التحقق من حالة المستخدم...</h2>
        </div>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-4 text-2xl font-bold text-gray-900">
            تم إعداد المؤسسة بنجاح!
          </h2>
          <p className="mt-2 text-gray-600">
            سيتم توجيهك إلى لوحة التحكم خلال ثوانٍ...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <Building2 className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            إعداد المؤسسة
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            أكمل إعداد مؤسستك للبدء في استخدام OZOO
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          {error && (
            <Alert variant="destructive">
              {error}
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <Label htmlFor="name">اسم المؤسسة *</Label>
              <Input
                id="name"
                type="text"
                {...register('name')}
                className={errors.name ? 'border-red-500' : ''}
                placeholder="أدخل اسم المؤسسة"
                autoFocus
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                يمكنك إضافة المزيد من التفاصيل لاحقاً من الإعدادات
              </p>
            </div>
          </div>

          <div>
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري إنشاء المؤسسة...
                </>
              ) : (
                'إنشاء المؤسسة والمتابعة إلى لوحة التحكم'
              )}
            </Button>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              بإنشاء المؤسسة، ستتمكن من الوصول إلى جميع مميزات OZOO
            </p>
          </div>
        </form>
      </div>
    </div>
  )
}
