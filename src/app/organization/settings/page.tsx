'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useDashboardProtection } from '@/hooks/useAuthGuard'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Alert } from '@/components/ui/Alert'
import { Loader2, Building2, Save, CheckCircle, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

// نموذج التحقق من بيانات المؤسسة
const organizationSettingsSchema = z.object({
  name: z.string().min(2, 'اسم المؤسسة يجب أن يكون حرفين على الأقل').max(100, 'اسم المؤسسة طويل جداً'),
  commercial_register: z.string().optional(),
  tax_number: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().min(1, 'يرجى اختيار الدولة'),
  registration_date: z.string().optional(),
  entity_type: z.string().optional(),
  admin_phone: z.string().optional(),
  admin_email: z.string().email('يرجى إدخال بريد إلكتروني صالح').optional().or(z.literal('')),
  digital_stamp_url: z.string().url('يرجى إدخال رابط صالح').optional().or(z.literal('')),
  business_type: z.string().optional(),
})

type OrganizationSettingsFormData = z.infer<typeof organizationSettingsSchema>

// خيارات الدول
const countries = [
  { value: 'SA', label: 'المملكة العربية السعودية' },
  { value: 'AE', label: 'الإمارات العربية المتحدة' },
  { value: 'KW', label: 'الكويت' },
  { value: 'QA', label: 'قطر' },
  { value: 'BH', label: 'البحرين' },
  { value: 'OM', label: 'عمان' },
  { value: 'JO', label: 'الأردن' },
  { value: 'LB', label: 'لبنان' },
  { value: 'EG', label: 'مصر' },
  { value: 'IQ', label: 'العراق' },
  { value: 'SY', label: 'سوريا' },
  { value: 'YE', label: 'اليمن' },
]

// خيارات نوع الكيان
const entityTypes = [
  { value: '', label: 'اختر نوع الكيان' },
  { value: 'sole_proprietorship', label: 'مؤسسة فردية' },
  { value: 'limited_liability', label: 'شركة ذات مسؤولية محدودة' },
  { value: 'joint_stock', label: 'شركة مساهمة' },
  { value: 'partnership', label: 'شركة تضامن' },
  { value: 'limited_partnership', label: 'شركة توصية بسيطة' },
  { value: 'cooperative', label: 'جمعية تعاونية' },
  { value: 'non_profit', label: 'مؤسسة غير ربحية' },
  { value: 'branch', label: 'فرع شركة أجنبية' },
  { value: 'other', label: 'أخرى' },
]

// خيارات النشاط التجاري
const businessTypes = [
  { value: '', label: 'اختر النشاط التجاري' },
  { value: 'retail', label: 'تجارة التجزئة' },
  { value: 'wholesale', label: 'تجارة الجملة' },
  { value: 'manufacturing', label: 'التصنيع' },
  { value: 'services', label: 'الخدمات' },
  { value: 'technology', label: 'التكنولوجيا' },
  { value: 'healthcare', label: 'الرعاية الصحية' },
  { value: 'education', label: 'التعليم' },
  { value: 'construction', label: 'البناء والتشييد' },
  { value: 'food_beverage', label: 'الأغذية والمشروبات' },
  { value: 'transportation', label: 'النقل والمواصلات' },
  { value: 'real_estate', label: 'العقارات' },
  { value: 'finance', label: 'الخدمات المالية' },
  { value: 'consulting', label: 'الاستشارات' },
  { value: 'other', label: 'أخرى' },
]

export default function OrganizationSettingsPage() {
  // يجب أن تكون جميع الـ hooks في البداية وبنفس الترتيب دائماً
  const router = useRouter()
  const { 
    isAuthenticated, 
    hasOrganization, 
    isLoading, 
    user, 
    status,
    retryCount,
    maxRetries,
    redirectToAppropriateRoute 
  } = useDashboardProtection()

  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [loadingData, setLoadingData] = useState(true)

  // useForm يجب أن يكون مع باقي الـ hooks
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<OrganizationSettingsFormData>({
    resolver: zodResolver(organizationSettingsSchema),
    defaultValues: {
      name: '',
      commercial_register: '',
      tax_number: '',
      address: '',
      city: '',
      country: 'SA',
      registration_date: '',
      entity_type: '',
      admin_phone: '',
      admin_email: '',
      digital_stamp_url: '',
      business_type: '',
    }
  })

  // دالة تحميل بيانات المؤسسة
  const loadOrganizationData = async () => {
    if (!user?.organization?.id) {
      setLoadingData(false)
      return
    }

    try {
      setLoadingData(true)
      setError(null)

      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', user.organization.id)
        .single()

      if (error) {
        console.error('Error loading organization data:', error)
        setError('حدث خطأ في تحميل بيانات المؤسسة')
        return
      }

      if (data) {
        // تعبئة النموذج بالبيانات الموجودة
        reset({
          name: data.name || '',
          commercial_register: data.commercial_register || '',
          tax_number: data.tax_number || '',
          address: data.address || '',
          city: data.city || '',
          country: data.country || 'SA',
          registration_date: data.registration_date || '',
          entity_type: data.entity_type || '',
          admin_phone: data.admin_phone || '',
          admin_email: data.admin_email || '',
          digital_stamp_url: data.digital_stamp_url || '',
          business_type: data.business_type || '',
        })
      }
    } catch (err) {
      console.error('Error loading organization data:', err)
      setError('حدث خطأ غير متوقع في تحميل البيانات')
    } finally {
      setLoadingData(false)
    }
  }

  // دالة حفظ البيانات
  const onSubmit = async (data: OrganizationSettingsFormData) => {
    if (!user?.organization?.id) {
      setError('لم يتم العثور على معرف المؤسسة')
      return
    }

    try {
      setError(null)
      setSuccess(false)

      const updateData = {
        name: data.name,
        commercial_register: data.commercial_register || null,
        tax_number: data.tax_number || null,
        address: data.address || null,
        city: data.city || null,
        country: data.country,
        registration_date: data.registration_date || null,
        entity_type: data.entity_type || null,
        admin_phone: data.admin_phone || null,
        admin_email: data.admin_email || null,
        digital_stamp_url: data.digital_stamp_url || null,
        business_type: data.business_type || null,
        updated_at: new Date().toISOString(),
      }

      const { error } = await supabase
        .from('organizations')
        .update(updateData)
        .eq('id', user.organization.id)

      if (error) {
        console.error('Error updating organization:', error)
        setError('حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.')
        return
      }

      setSuccess(true)

      // إخفاء رسالة النجاح بعد 3 ثوان
      setTimeout(() => {
        setSuccess(false)
      }, 3000)

    } catch (err) {
      console.error('Error updating organization:', err)
      setError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.')
    }
  }

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    if (user?.organization?.id && !loadingData) {
      loadOrganizationData()
    }
  }, [user?.organization?.id])

  // إظهار شاشة التحميل أثناء فحص حالة المصادقة
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md">
          <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            جاري تحميل إعدادات المؤسسة...
          </h2>
          <p className="text-gray-600 mb-4">
            يتم تحميل بيانات المستخدم والمؤسسة
          </p>
          
          {retryCount > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <Loader2 className="h-5 w-5 text-yellow-600 ml-2 animate-spin" />
                <p className="text-sm text-yellow-800">
                  محاولة إعادة التحميل ({retryCount}/{maxRetries})
                </p>
              </div>
            </div>
          )}

          {retryCount >= maxRetries && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-center">
                <p className="text-sm text-red-800 mb-2">
                  تعذر تحميل البيانات. يرجى المحاولة مرة أخرى.
                </p>
              </div>
              <Button 
                onClick={() => window.location.reload()} 
                variant="outline" 
                size="sm"
                className="mt-2"
              >
                <Loader2 className="h-4 w-4 ml-2" />
                إعادة المحاولة
              </Button>
            </div>
          )}
        </div>
      </div>
    )
  }

  // إذا لم يكن مصادق أو لا يملك مؤسسة، سيتم التوجيه تلقائياً
  if (!isAuthenticated || !hasOrganization) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 text-blue-600 animate-spin mb-4" />
          <p className="text-gray-600">جاري التوجيه...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link 
                href="/dashboard"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 ml-2" />
                العودة إلى لوحة التحكم
              </Link>
            </div>
            <div className="flex items-center">
              <Building2 className="h-6 w-6 text-blue-600 ml-3" />
              <h1 className="text-2xl font-bold text-gray-900">إعدادات المؤسسة</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">بيانات المؤسسة</h2>
            <p className="text-sm text-gray-600 mt-1">
              قم بتحديث معلومات مؤسستك والبيانات الضريبية والقانونية
            </p>
          </div>

          <div className="p-6">
            {error && (
              <Alert variant="destructive" className="mb-6">
                {error}
              </Alert>
            )}

            {success && (
              <Alert className="mb-6 bg-green-50 border-green-200 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span>تم حفظ البيانات بنجاح</span>
              </Alert>
            )}

            {loadingData ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 text-blue-600 animate-spin ml-3" />
                <span className="text-gray-600">جاري تحميل بيانات المؤسسة...</span>
              </div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* المعلومات الأساسية */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* اسم المؤسسة */}
                  <div className="md:col-span-2">
                    <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      اسم المؤسسة *
                    </Label>
                    <Input
                      id="name"
                      type="text"
                      {...register('name')}
                      className={`w-full px-4 py-3 border-2 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${
                        errors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="أدخل اسم المؤسسة"
                    />
                    {errors.name && (
                      <p className="mt-2 text-sm text-red-600">{errors.name.message}</p>
                    )}
                  </div>

                  {/* السجل التجاري */}
                  <div>
                    <Label htmlFor="commercial_register" className="block text-sm font-medium text-gray-700 mb-2">
                      السجل التجاري
                    </Label>
                    <Input
                      id="commercial_register"
                      type="text"
                      {...register('commercial_register')}
                      className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                      placeholder="رقم السجل التجاري"
                    />
                  </div>

                  {/* الرقم الضريبي */}
                  <div>
                    <Label htmlFor="tax_number" className="block text-sm font-medium text-gray-700 mb-2">
                      الرقم الضريبي
                    </Label>
                    <Input
                      id="tax_number"
                      type="text"
                      {...register('tax_number')}
                      className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                      placeholder="الرقم الضريبي"
                    />
                  </div>

                  {/* نوع الكيان */}
                  <div>
                    <Label htmlFor="entity_type" className="block text-sm font-medium text-gray-700 mb-2">
                      نوع الكيان
                    </Label>
                    <select
                      id="entity_type"
                      {...register('entity_type')}
                      className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                    >
                      {entityTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* النشاط التجاري */}
                  <div>
                    <Label htmlFor="business_type" className="block text-sm font-medium text-gray-700 mb-2">
                      النشاط التجاري
                    </Label>
                    <select
                      id="business_type"
                      {...register('business_type')}
                      className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                    >
                      {businessTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* تاريخ التسجيل */}
                  <div>
                    <Label htmlFor="registration_date" className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ التسجيل
                    </Label>
                    <Input
                      id="registration_date"
                      type="date"
                      {...register('registration_date')}
                      className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                    />
                  </div>
                </div>

                {/* العنوان والموقع */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">العنوان والموقع</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* العنوان */}
                    <div className="md:col-span-2">
                      <Label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان
                      </Label>
                      <Input
                        id="address"
                        type="text"
                        {...register('address')}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                        placeholder="العنوان التفصيلي"
                      />
                    </div>

                    {/* المدينة */}
                    <div>
                      <Label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                        المدينة
                      </Label>
                      <Input
                        id="city"
                        type="text"
                        {...register('city')}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                        placeholder="اسم المدينة"
                      />
                    </div>

                    {/* الدولة */}
                    <div>
                      <Label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
                        الدولة *
                      </Label>
                      <select
                        id="country"
                        {...register('country')}
                        className={`w-full px-4 py-3 border-2 rounded-lg text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${
                          errors.country ? 'border-red-500' : 'border-gray-300'
                        }`}
                      >
                        {countries.map((country) => (
                          <option key={country.value} value={country.value}>
                            {country.label}
                          </option>
                        ))}
                      </select>
                      {errors.country && (
                        <p className="mt-2 text-sm text-red-600">{errors.country.message}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* معلومات الاتصال الإدارية */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">معلومات الاتصال الإدارية</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* رقم الهاتف الإداري */}
                    <div>
                      <Label htmlFor="admin_phone" className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف الإداري
                      </Label>
                      <Input
                        id="admin_phone"
                        type="tel"
                        {...register('admin_phone')}
                        className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors"
                        placeholder="+966501234567"
                      />
                    </div>

                    {/* البريد الإلكتروني الإداري */}
                    <div>
                      <Label htmlFor="admin_email" className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني الإداري
                      </Label>
                      <Input
                        id="admin_email"
                        type="email"
                        {...register('admin_email')}
                        className={`w-full px-4 py-3 border-2 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${
                          errors.admin_email ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {errors.admin_email && (
                        <p className="mt-2 text-sm text-red-600">{errors.admin_email.message}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* الختم الإلكتروني */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">الختم الإلكتروني</h3>
                  <div>
                    <Label htmlFor="digital_stamp_url" className="block text-sm font-medium text-gray-700 mb-2">
                      رابط الختم الإلكتروني (اختياري)
                    </Label>
                    <Input
                      id="digital_stamp_url"
                      type="url"
                      {...register('digital_stamp_url')}
                      className={`w-full px-4 py-3 border-2 rounded-lg text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-colors ${
                        errors.digital_stamp_url ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="https://example.com/digital-stamp.png"
                    />
                    {errors.digital_stamp_url && (
                      <p className="mt-2 text-sm text-red-600">{errors.digital_stamp_url.message}</p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      رابط مباشر لصورة الختم الإلكتروني المعتمد للمؤسسة
                    </p>
                  </div>
                </div>

                {/* أزرار الحفظ */}
                <div className="border-t pt-6">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      {isDirty && !isSubmitting && (
                        <span className="text-yellow-600">
                          • يوجد تغييرات غير محفوظة
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          if (isDirty) {
                            const confirmReset = window.confirm('هل تريد إلغاء التغييرات غير المحفوظة؟')
                            if (confirmReset) {
                              loadOrganizationData()
                            }
                          }
                        }}
                        disabled={isSubmitting || !isDirty}
                        className="px-6 py-2"
                      >
                        إلغاء التغييرات
                      </Button>
                      <Button
                        type="submit"
                        disabled={isSubmitting || !isDirty}
                        className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                        style={{ backgroundColor: isSubmitting || !isDirty ? undefined : '#2563eb' }}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-5 w-5 ml-2 animate-spin" />
                            جاري الحفظ...
                          </>
                        ) : (
                          <>
                            <Save className="h-5 w-5 ml-2" />
                            حفظ البيانات
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
