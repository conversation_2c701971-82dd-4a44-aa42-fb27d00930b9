# 🔍 دليل تشخيص خطأ جدول المؤسسات

## 🎯 **الهدف:** حل خطأ `Organization search error: {}`

---

## 📋 **خطوات التشخيص**

### **1. افتح Console المتصفح**
```
F12 → Console Tab
```

### **2. ابحث عن الرسائل الجديدة:**

#### **✅ رسائل التشخيص المتوقعة:**
```
Starting organization check for user: [user-id]
Organizations table is accessible
Profile data retrieved: { organization_id: null }
organization_id is empty, searching in organizations table for owner_id: [user-id]
```

#### **❌ رسائل الخطأ المحتملة:**
```
Organizations table might not exist or have access issues: [error details]
Organization search error details: { code: "...", message: "...", ... }
```

---

## 🔧 **الحلول حسب نوع الخطأ**

### **🚨 المشكلة 1: جدول organizations غير موجود**

#### **الأعراض:**
```
Organizations table might not exist or have access issues: {
  code: "42P01",
  message: "relation \"public.organizations\" does not exist"
}
```

#### **الحل:**
```sql
-- في Supabase SQL Editor
CREATE TABLE organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  owner_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهرس للأداء
CREATE INDEX idx_organizations_owner_id ON organizations(owner_id);
```

### **🚨 المشكلة 2: مشاكل في صلاحيات RLS**

#### **الأعراض:**
```
Organization search error details: {
  code: "42501",
  message: "permission denied for table organizations"
}
```

#### **الحل:**
```sql
-- تفعيل RLS
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- إضافة سياسات الأمان
CREATE POLICY "Users can view their own organizations" ON organizations
  FOR SELECT USING (owner_id = auth.uid());

CREATE POLICY "Users can insert their own organizations" ON organizations
  FOR INSERT WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own organizations" ON organizations
  FOR UPDATE USING (owner_id = auth.uid());
```

### **🚨 المشكلة 3: عمود owner_id غير موجود**

#### **الأعراض:**
```
Organization search error details: {
  code: "42703",
  message: "column \"owner_id\" does not exist"
}
```

#### **الحل:**
```sql
-- إضافة عمود owner_id
ALTER TABLE organizations 
ADD COLUMN owner_id UUID REFERENCES auth.users(id);

-- إضافة فهرس
CREATE INDEX idx_organizations_owner_id ON organizations(owner_id);
```

### **🚨 المشكلة 4: لا توجد مؤسسة للمستخدم**

#### **الأعراض:**
```
Organization search error details: {
  code: "PGRST116",
  message: "JSON object requested, multiple (or no) rows returned",
  details: "The result contains 0 rows"
}
```

#### **الحل:**
```sql
-- إنشاء مؤسسة للمستخدم
INSERT INTO organizations (name, owner_id)
VALUES ('مؤسسة تجريبية', 'USER_ID_HERE');

-- أو استخدام البريد الإلكتروني للبحث عن المستخدم
INSERT INTO organizations (name, owner_id)
SELECT 'مؤسسة تجريبية', id 
FROM auth.users 
WHERE email = '<EMAIL>';
```

### **🚨 المشكلة 5: مشاكل في الاتصال**

#### **الأعراض:**
```
Organization search error details: {
  code: "NETWORK_ERROR",
  message: "Failed to fetch"
}
```

#### **الحل:**
1. تحقق من اتصال الإنترنت
2. تحقق من إعدادات Supabase في `.env.local`
3. تحقق من حالة خدمة Supabase

---

## 🛠️ **إعداد جدول organizations كامل**

### **1. إنشاء الجدول:**
```sql
CREATE TABLE organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  owner_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **2. إضافة الفهارس:**
```sql
CREATE INDEX idx_organizations_owner_id ON organizations(owner_id);
CREATE INDEX idx_organizations_created_at ON organizations(created_at);
```

### **3. تفعيل RLS:**
```sql
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
```

### **4. إضافة السياسات:**
```sql
-- سياسة القراءة
CREATE POLICY "Users can view their own organizations" ON organizations
  FOR SELECT USING (owner_id = auth.uid());

-- سياسة الإدراج
CREATE POLICY "Users can insert their own organizations" ON organizations
  FOR INSERT WITH CHECK (owner_id = auth.uid());

-- سياسة التحديث
CREATE POLICY "Users can update their own organizations" ON organizations
  FOR UPDATE USING (owner_id = auth.uid());

-- سياسة الحذف
CREATE POLICY "Users can delete their own organizations" ON organizations
  FOR DELETE USING (owner_id = auth.uid());
```

### **5. إضافة trigger للتحديث التلقائي:**
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON organizations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

---

## 📊 **إنشاء بيانات تجريبية**

### **1. إنشاء مؤسسة لمستخدم موجود:**
```sql
-- استبدل '<EMAIL>' ببريد المستخدم الفعلي
INSERT INTO organizations (name, description, owner_id)
SELECT 
  'شركة OZOO للمحاسبة',
  'شركة متخصصة في الحلول المحاسبية',
  id 
FROM auth.users 
WHERE email = '<EMAIL>';
```

### **2. ربط المؤسسة بالملف الشخصي:**
```sql
-- تحديث جدول profiles
UPDATE profiles 
SET organization_id = (
  SELECT o.id 
  FROM organizations o 
  JOIN auth.users u ON o.owner_id = u.id 
  WHERE u.email = '<EMAIL>'
)
WHERE id = (
  SELECT id 
  FROM auth.users 
  WHERE email = '<EMAIL>'
);
```

---

## ✅ **التحقق من الإعداد**

### **1. فحص الجداول:**
```sql
-- فحص وجود الجدول
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'organizations';

-- فحص الأعمدة
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'organizations';
```

### **2. فحص البيانات:**
```sql
-- فحص المؤسسات
SELECT id, name, owner_id, created_at 
FROM organizations;

-- فحص ربط المؤسسات بالمستخدمين
SELECT 
  u.email,
  p.organization_id,
  o.name as organization_name
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
LEFT JOIN organizations o ON p.organization_id = o.id;
```

### **3. فحص السياسات:**
```sql
-- فحص سياسات RLS
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'organizations';
```

---

## 🎯 **النتيجة المتوقعة**

### **✅ بعد تطبيق الحلول:**

#### **في Console المتصفح:**
```
Starting organization check for user: abc123
Organizations table is accessible
Profile data retrieved: { organization_id: null }
organization_id is empty, searching in organizations table for owner_id: abc123
Organization found: {
  id: "def456",
  name: "شركة OZOO للمحاسبة",
  owner_id: "abc123"
}
Profile updated successfully with organization_id: def456
Organization found, redirecting to dashboard
```

#### **في التطبيق:**
- ✅ تسجيل دخول ناجح
- ✅ توجيه تلقائي إلى `/dashboard`
- ✅ لا توجد أخطاء في Console

---

## 📞 **الدعم الإضافي**

### **إذا استمرت المشكلة:**

1. **تحقق من ملف `.env.local`:**
```
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

2. **تحقق من حالة Supabase:**
   - زر [Supabase Status](https://status.supabase.com/)

3. **تحقق من logs Supabase:**
   - Dashboard → Logs → API Logs

4. **تحقق من Authentication:**
   - Dashboard → Authentication → Users

---

## 🚀 **الخلاصة**

مع التحسينات الجديدة في الكود، ستحصل على:

- ✅ **تشخيص دقيق للمشكلة**
- ✅ **رسائل خطأ واضحة ومفيدة**
- ✅ **إرشادات محددة للحل**
- ✅ **logging شامل لكل خطوة**

**افتح Console المتصفح الآن لرؤية التفاصيل الجديدة! 🔍**

---

*دليل التشخيص - إعداد: ${new Date().toLocaleString('ar-SA')}*
*بواسطة: Augment Agent*
*حالة الدليل: ✅ شامل وجاهز للاستخدام*
