-- إصلاح جدول organizations بإضافة عمود business_type المفقود
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- التحقق من وجود العمود أولاً
DO $$
BEGIN
    -- التحقق من وجود عمود business_type
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'business_type'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود business_type
        ALTER TABLE public.organizations 
        ADD COLUMN business_type VARCHAR(100);
        
        RAISE NOTICE 'تم إضافة عمود business_type إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود business_type موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود email
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'email'
        AND table_schema = 'public'
    ) THEN
        -- إضا<PERSON>ة عمود email
        ALTER TABLE public.organizations 
        ADD COLUMN email VARCHAR(255);
        
        RAISE NOTICE 'تم إضافة عمود email إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود email موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود commercial_register
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'commercial_register'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود commercial_register
        ALTER TABLE public.organizations 
        ADD COLUMN commercial_register VARCHAR(100);
        
        RAISE NOTICE 'تم إضافة عمود commercial_register إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود commercial_register موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود address
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'address'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود address
        ALTER TABLE public.organizations 
        ADD COLUMN address TEXT;
        
        RAISE NOTICE 'تم إضافة عمود address إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود address موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود registration_date
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'registration_date'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود registration_date
        ALTER TABLE public.organizations 
        ADD COLUMN registration_date DATE;
        
        RAISE NOTICE 'تم إضافة عمود registration_date إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود registration_date موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود entity_type
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'entity_type'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود entity_type
        ALTER TABLE public.organizations 
        ADD COLUMN entity_type VARCHAR(100);
        
        RAISE NOTICE 'تم إضافة عمود entity_type إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود entity_type موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود admin_phone
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'admin_phone'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود admin_phone
        ALTER TABLE public.organizations 
        ADD COLUMN admin_phone VARCHAR(20);
        
        RAISE NOTICE 'تم إضافة عمود admin_phone إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود admin_phone موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود admin_email
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'admin_email'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود admin_email
        ALTER TABLE public.organizations 
        ADD COLUMN admin_email VARCHAR(255);
        
        RAISE NOTICE 'تم إضافة عمود admin_email إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود admin_email موجود بالفعل في جدول organizations';
    END IF;
    
    -- التحقق من وجود عمود digital_stamp_url
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'organizations' 
        AND column_name = 'digital_stamp_url'
        AND table_schema = 'public'
    ) THEN
        -- إضافة عمود digital_stamp_url
        ALTER TABLE public.organizations 
        ADD COLUMN digital_stamp_url TEXT;
        
        RAISE NOTICE 'تم إضافة عمود digital_stamp_url إلى جدول organizations';
    ELSE
        RAISE NOTICE 'عمود digital_stamp_url موجود بالفعل في جدول organizations';
    END IF;
    
END $$;

-- عرض بنية الجدول النهائية للتأكد
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'organizations' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- رسالة تأكيد
SELECT 'تم إصلاح جدول organizations بنجاح! يمكنك الآن استخدام جميع الحقول المطلوبة.' as message;
