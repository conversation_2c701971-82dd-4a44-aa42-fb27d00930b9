-- إنشاء جدول organizations إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255),
    business_type VARCHAR(100),
    logo_url TEXT,
    commercial_register VARCHAR(100),
    tax_number VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(2) DEFAULT 'SA',
    registration_date DATE,
    entity_type VARCHAR(100),
    admin_phone VARCHAR(20),
    admin_email VARCHAR(255),
    digital_stamp_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس على owner_id للبحث السريع
CREATE INDEX IF NOT EXISTS idx_organizations_owner_id ON public.organizations(owner_id);

-- إنشاء فهرس على email للبحث السريع
CREATE INDEX IF NOT EXISTS idx_organizations_email ON public.organizations(email);

-- إنشاء قيد فريد على owner_id (مؤسسة واحدة لكل مستخدم)
ALTER TABLE public.organizations 
ADD CONSTRAINT unique_owner_id UNIQUE (owner_id);

-- تمكين Row Level Security (RLS)
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة للقراءة: المستخدم يمكنه قراءة مؤسسته فقط
CREATE POLICY "Users can read their own organization" ON public.organizations
    FOR SELECT USING (auth.uid() = owner_id);

-- إنشاء سياسة للإدراج: المستخدم يمكنه إنشاء مؤسسة واحدة فقط
CREATE POLICY "Users can insert their own organization" ON public.organizations
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- إنشاء سياسة للتحديث: المستخدم يمكنه تحديث مؤسسته فقط
CREATE POLICY "Users can update their own organization" ON public.organizations
    FOR UPDATE USING (auth.uid() = owner_id);

-- إنشاء سياسة للحذف: المستخدم يمكنه حذف مؤسسته فقط
CREATE POLICY "Users can delete their own organization" ON public.organizations
    FOR DELETE USING (auth.uid() = owner_id);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at عند التحديث
CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON public.organizations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تحديث جدول profiles إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name VARCHAR(255),
    avatar_url TEXT,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تمكين RLS على جدول profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات لجدول profiles
CREATE POLICY "Users can read their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- إنشاء trigger لتحديث updated_at في profiles
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إنشاء فهارس على جدول profiles
CREATE INDEX IF NOT EXISTS idx_profiles_organization_id ON public.profiles(organization_id);

-- إدراج ملف شخصي تلقائياً عند إنشاء مستخدم جديد
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء trigger لإنشاء ملف شخصي تلقائياً
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- منح الصلاحيات المناسبة
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.organizations TO authenticated;
GRANT ALL ON public.profiles TO authenticated;
GRANT SELECT ON public.organizations TO anon;
GRANT SELECT ON public.profiles TO anon;
